package endpoint_application

import (
	"marketing/internal/api"
	"marketing/internal/api/endpoint_application"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint_application"
	"strconv"

	"github.com/gin-gonic/gin"
)

// EndpointPolicyHandler 终端政策处理器接口
type EndpointPolicyHandler interface {
	// CreateEndpointPolicy 创建终端政策
	CreateEndpointPolicy(c *gin.Context)
	// DeleteEndpointPolicy 删除终端政策
	DeleteEndpointPolicy(c *gin.Context)
	// UpdateEndpointPolicy 更新终端政策
	UpdateEndpointPolicy(c *gin.Context)
	// GetEndpointPolicyByID 根据ID获取终端政策
	GetEndpointPolicyByID(c *gin.Context)
	// GetEndpointPolicyList 获取终端政策列表
	GetEndpointPolicyList(c *gin.Context)
	//UpdateEndpointPolicyEnabled 修改政策状态
	UpdateEndpointPolicyEnabled(c *gin.Context)
}

// EndpointPolicyHandlerImpl 终端政策处理器实现
type EndpointPolicyHandlerImpl struct {
	service service.EndpointPolicyService
}

// NewEndpointPolicyHandler 创建终端政策处理器实例
func NewEndpointPolicyHandler(service service.EndpointPolicyService) EndpointPolicyHandler {
	return &EndpointPolicyHandlerImpl{
		service: service,
	}
}

// CreateEndpointPolicy 创建终端政策
func (h *EndpointPolicyHandlerImpl) CreateEndpointPolicy(c *gin.Context) {
	var req endpoint_application.CreateEndpointPolicyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.service.CreateEndpointPolicy(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// DeleteEndpointPolicy 删除终端政策
func (h *EndpointPolicyHandlerImpl) DeleteEndpointPolicy(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID参数"))
		return
	}

	err = h.service.DeleteEndpointPolicy(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// UpdateEndpointPolicy 更新终端政策
func (h *EndpointPolicyHandlerImpl) UpdateEndpointPolicy(c *gin.Context) {
	var req endpoint_application.UpdateEndpointPolicyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// 从URL参数获取ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID参数"))
		return
	}
	req.ID = id

	err = h.service.UpdateEndpointPolicy(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// UpdateEndpointPolicyEnabled 更新终端政策
func (h *EndpointPolicyHandlerImpl) UpdateEndpointPolicyEnabled(c *gin.Context) {
	var req endpoint_application.UpdateEndpointPolicyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// 从URL参数获取ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID参数"))
		return
	}
	req.ID = id

	err = h.service.UpdateEndpointPolicyEnabled(c, id, req.Enabled)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// GetEndpointPolicyByID 根据ID获取终端政策
func (h *EndpointPolicyHandlerImpl) GetEndpointPolicyByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID参数"))
		return
	}

	policy, err := h.service.GetEndpointPolicyByID(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, policy)
}

// GetEndpointPolicyList 获取终端政策列表
func (h *EndpointPolicyHandlerImpl) GetEndpointPolicyList(c *gin.Context) {
	var req endpoint_application.EndpointPolicyListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	// 设置默认分页参数
	req.SetDefaults()
	policies, total, err := h.service.GetEndpointPolicyList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	// 设置默认分页参数
	req.SetDefaults()
	// 构建分页响应
	resp := api.NewPagedResponse(policies, total, req.Page, req.PageSize)
	handler.Success(c, resp)
}
