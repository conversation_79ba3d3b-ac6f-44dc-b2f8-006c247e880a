# 终端钩子处理器使用说明

## 概述
`EndpointHookProcessor` 是一个终端相关的钩子处理器，用于在终端创建或更新后执行相关的业务逻辑，如发送通知、触发后续业务处理等。

## 功能特性

### 1. 申请通过通知
- 当终端创建或更新完成后，发送申请通过通知
- 调用服务的 `SendNotify` 方法
- 支持短信和企微通知

### 2. 终端创建后钩子
- 终端创建或更新后的业务处理
- 调用服务的 `EndpointCreated` 方法
- 可以触发其他系统的同步、缓存更新等

### 3. 容错处理
- 所有钩子方法都是可选的
- 方法不存在或执行失败不会中断主流程

## 使用场景

### 适用场景：
- **新建店铺审核通过**: 终端创建后发送通知
- **老店改造审核通过**: 终端更新后发送通知
- **终端信息同步**: 触发其他系统的数据同步
- **缓存更新**: 更新相关的缓存数据
- **业务集成**: 与其他业务系统的集成点

## 处理器配置

### 1. 在工作流模板中配置
```json
{
  "state": "approved",
  "processors": [
    {
      "name": "approved",
      "config": {}
    },
    {
      "name": "endpoint_hook",
      "config": {}
    }
  ]
}
```

### 2. 处理器执行顺序
建议的处理器执行顺序：
1. `approved` 或 `renew` - 创建/更新终端
2. `endpoint_hook` - 执行钩子方法
3. 其他业务处理器

## 数据流处理

### 输入数据要求
处理器从 `req.Data` 中获取：
- `service`: 服务实例（必须实现相关接口）
- `application`: 申请信息
- `endpoint_code`: 终端编码（用于通知）
- `endpoint`: 终端信息（用于钩子）

### 处理逻辑
```go
// 1. 如果有终端编码，发送申请通过通知
if endpointCode != "" {
    service.SendNotify(ctx, applyModel, endpointCode)
}

// 2. 如果有终端信息，执行创建后钩子
if endpoint != nil {
    service.EndpointCreated(ctx, endpoint)
}
```

## 服务接口要求

### SendNotify 接口
服务需要实现此接口来支持通知功能：
```go
type NotifyService interface {
    SendNotify(ctx *gin.Context, apply *model.EndpointApplication, code string)
}
```

### EndpointCreated 接口
服务需要实现此接口来支持终端创建钩子：
```go
type EndpointHookService interface {
    EndpointCreated(ctx *gin.Context, endpoint *model.Endpoint)
}
```

## 实现细节

### 1. 接口检查
```go
// 动态检查服务是否实现了相关接口
if serviceWithNotify, ok := service.(interface {
    SendNotify(*gin.Context, *model.EndpointApplication, string)
}); ok {
    serviceWithNotify.SendNotify(ctx, applyModel, endpointCode)
}
```

### 2. 容错机制
- 服务实例不存在 → 跳过处理
- 申请信息不存在 → 跳过处理
- 接口未实现 → 跳过对应的处理
- 所有错误都不会中断主流程

### 3. 数据验证
- 检查数据类型是否正确
- 检查必要的数据是否存在
- 提供默认的空处理

## 与其他处理器的协作

### 与 ApprovedProcessor 协作
```yaml
processors:
  - name: "approved"      # 创建终端
  - name: "endpoint_hook" # 发送通知和执行钩子
```

### 与 RenewProcessor 协作
```yaml
processors:
  - name: "renew"         # 更新终端
  - name: "endpoint_hook" # 发送通知和执行钩子
```

## 使用示例

### 工作流模板配置
```yaml
name: "建店审批流程"
states:
  - id: 1
    name: "待审核"
    type: "pending"
  - id: 2
    name: "审核通过"
    type: "approved"
    processors:
      - name: "approved"
      - name: "endpoint_hook"
      - name: "material"

transitions:
  - from: 1
    to: 2
    action: "approve"
```

### 服务实现示例
```go
// 实现通知接口
func (s *endpointApplyService) SendNotify(ctx *gin.Context, apply *model.EndpointApplication, code string) {
    // 发送短信通知
    // 发送企微通知
}

// 实现终端创建钩子
func (s *endpointApplyService) EndpointCreated(ctx *gin.Context, endpoint *model.Endpoint) {
    // 同步到其他系统
    // 更新缓存
    // 触发其他业务逻辑
}
```

## 扩展说明

### 添加新的钩子
如果需要添加新的钩子方法：

1. 定义新的接口
```go
type CustomHookService interface {
    OnEndpointUpdated(ctx *gin.Context, endpoint *model.Endpoint) error
}
```

2. 在处理器中添加调用
```go
if serviceWithCustomHook, ok := service.(CustomHookService); ok {
    serviceWithCustomHook.OnEndpointUpdated(ctx, endpoint)
}
```

3. 在服务中实现接口
```go
func (s *endpointApplyService) OnEndpointUpdated(ctx *gin.Context, endpoint *model.Endpoint) error {
    // 自定义业务逻辑
    return nil
}
```

## 注意事项

1. **接口可选**: 所有接口都是可选的，不实现不会报错
2. **容错处理**: 钩子方法执行失败不会中断主流程
3. **性能考虑**: 钩子方法应该尽量轻量，避免长时间阻塞
4. **事务独立**: 钩子方法通常在主事务外执行，需要独立处理事务
5. **异步处理**: 对于耗时的钩子操作，建议使用异步处理

## 重构说明

### 从 NotificationProcessor 重构而来
原来的 `NotificationProcessor` 实际上不是一个纯粹的通知处理器，而是一个终端相关的钩子处理器：

- **原名称**: `NotificationProcessor` → **新名称**: `EndpointHookProcessor`
- **原处理器名**: `"notification"` → **新处理器名**: `"endpoint_hook"`
- **功能不变**: 保持原有的通知和钩子功能
- **语义更清晰**: 名称更准确地反映了处理器的实际用途

这个处理器的核心作用是作为终端创建/更新后的业务集成点，而不是单纯的通知功能。
