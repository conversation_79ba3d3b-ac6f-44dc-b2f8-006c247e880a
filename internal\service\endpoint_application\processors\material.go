package processors

import (
	"marketing/internal/api/endpoint_application"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
)

// MaterialProcessor 物料处理器
type MaterialProcessor struct{}

// NewMaterialProcessor 创建物料处理器
func NewMaterialProcessor() *MaterialProcessor {
	return &MaterialProcessor{}
}

// Process 处理物料支持状态 - 直接执行物料支持逻辑
func (p *MaterialProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}
	// 直接处理物料支持逻辑，不需要判断状态
	return p.handleMaterialSupported(ctx, txRepo, req, updateData)
}

// handleMaterialSupported 处理物料支持完成状态
func (p *MaterialProcessor) handleMaterialSupported(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, req *statemachine.StateTransitionRequest, updateData map[string]any) error {

	// 处理物料列表
	if materialListData, exists := req.Data["material_list"]; exists {
		materialList, ok := materialListData.([]*endpoint_application.EndpointMaterialSupport)
		if !ok {
			return errors.NewErr("物料列表格式错误")
		}

		// 转换为模型
		var materials []*model.EndpointMaterialSupport
		for _, item := range materialList {
			materials = append(materials, &model.EndpointMaterialSupport{
				ApplicationID:    int(req.ApplicationID),
				Name:             item.Name,
				Num:              item.Num,
				Pic:              utils.DeletePrefix(item.Pic),
				Price:            item.Price,
				ProductionNumber: item.ProductionNumber,
				Thumbnail:        utils.DeletePrefix(item.Thumbnail),
			})
		}

		// 保存物料数据
		if err := txRepo.CreateEndpointMaterialSupport(ctx, materials); err != nil {
			return err
		}

		// 计算物料总金额
		var totalAmount float64
		for _, material := range materials {
			totalAmount += material.Price * float64(material.Num)
		}

	}

	return nil
}

// GetName 获取处理器名称
func (p *MaterialProcessor) GetName() string {
	return "material"
}
