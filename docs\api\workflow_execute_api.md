# 通用工作流执行 API 文档

## 概述

通用工作流执行接口提供了统一的方式来处理各种工作流的状态转换，目前支持：
- `endpoint_application` - 终端申请工作流
- 后续可扩展支持 `warranty`、`reimbursement` 等其他工作流

## 接口列表

### 1. 执行工作流 - POST /admin/workflow/execute

执行指定的工作流操作。

**请求参数：**
```json
{
  "workflow_type": "endpoint_application",  // 工作流类型
  "entity_id": 123,                        // 实体ID（如申请ID）
  "action": "approve",                     // 执行的动作
  "remark": "审核通过",                     // 备注（可选）
  "extend": {                              // 扩展字段（可选）
    "reason": "符合条件"
  },
  "data": {                                // 附加数据（可选）
    "custom_field": "value"
  }
}
```

**响应示例：**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "success": true,
    "message": "操作成功",
    "before_state": 10,    // 操作前状态
    "after_state": 100,    // 操作后状态
    "next_state": 200,     // 下一个可能状态
    "data": {              // 附加数据
      "endpoint_id": 456,
      "endpoint_code": "EP001"
    }
  }
}
```

### 2. 获取工作流状态 - GET /admin/workflow/state

获取指定实体的当前工作流状态和可用操作。

**请求参数：**
- `workflow_type`: 工作流类型 (必填)
- `entity_id`: 实体ID (必填)

**示例：**
```
GET /admin/workflow/state?workflow_type=endpoint_application&entity_id=123
```

**响应示例：**
```json
{
  "ok": 1,
  "msg": "ok", 
  "data": {
    "current_state": 10,      // 当前状态
    "next_state": 100,        // 下一个状态
    "workflow_type": "endpoint_application",
    "entity_id": 123,
    "available_actions": [    // 可用操作列表
      {
        "type": "approve",
        "name": "审核通过",
        "description": "审核申请并通过",
        "target_state": 100,
        "fields": [
          {
            "name": "remark",
            "type": "string",
            "label": "审核意见",
            "required": false
          }
        ]
      },
      {
        "type": "reject", 
        "name": "审核拒绝",
        "description": "审核申请并拒绝",
        "target_state": -100,
        "fields": [
          {
            "name": "remark",
            "type": "string", 
            "label": "拒绝原因",
            "required": true
          }
        ]
      }
    ]
  }
}
```

### 3. 获取可用操作 - GET /admin/workflow/available-actions

获取指定实体的可用操作列表（简化版本）。

**请求参数：**
- `workflow_type`: 工作流类型 (必填)
- `entity_id`: 实体ID (必填)

**示例：**
```
GET /admin/workflow/available-actions?workflow_type=endpoint_application&entity_id=123
```

**响应示例：**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "workflow_type": "endpoint_application",
    "entity_id": 123,
    "actions": [
      {
        "type": "approve",
        "name": "审核通过", 
        "description": "审核申请并通过",
        "target_state": 100
      }
    ]
  }
}
```

## 工作流类型说明

### endpoint_application (终端申请工作流)

**常用动作：**
- `approve` - 审核通过
- `reject` - 审核拒绝
- `material_support` - 物料支持
- `postback` - 建店回传
- `record` - 入账确认
- `writeoff` - 核销

**状态说明：**
- `10` - 待审核
- `100` - 已批准
- `-100` - 已拒绝
- `200` - 物料支持
- `300` - 建店回传
- `400` - 入账确认

## 使用示例

### 1. 审核终端申请

```bash
# 审核通过
curl -X POST /admin/workflow/execute \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_type": "endpoint_application",
    "entity_id": 123,
    "action": "approve",
    "remark": "审核通过，符合开店条件"
  }'

# 审核拒绝
curl -X POST /admin/workflow/execute \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_type": "endpoint_application", 
    "entity_id": 123,
    "action": "reject",
    "remark": "资料不全，请重新提交"
  }'
```

### 2. 查看申请状态

```bash
curl -X GET "/admin/workflow/state?workflow_type=endpoint_application&entity_id=123"
```

### 3. 物料支持操作

```bash
curl -X POST /admin/workflow/execute \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_type": "endpoint_application",
    "entity_id": 123,
    "action": "material_support",
    "data": {
      "materials": [
        {"type": "装修物料", "amount": 5000},
        {"type": "设备物料", "amount": 3000}
      ]
    }
  }'
```

## 错误处理

**常见错误响应：**

```json
{
  "ok": 0,
  "msg": "不支持的工作流类型: unknown_workflow"
}
```

```json
{
  "ok": 0,
  "msg": "终端申请不存在"
}
```

```json
{
  "ok": 0,
  "msg": "状态转换不合法: 当前状态不允许此操作"
}
```

## 扩展性

该接口设计为通用接口，可以轻松扩展支持其他工作流类型：

1. 在 `WorkflowService` 中添加新的工作流类型处理
2. 实现对应的 `execute{WorkflowType}Workflow` 方法
3. 添加相应的状态获取和操作获取方法

例如，添加保修工作流：

```go
case "warranty":
    return s.executeWarrantyWorkflow(ctx, req)
```

这样就可以用相同的接口处理保修工作流了。
