# 处理器驱动的工作流架构

## 架构概述

现在所有的业务处理逻辑都通过处理器来实现，状态机在执行状态转换时会自动调用配置的处理器，实现了真正的配置驱动业务逻辑。

## 工作流程

### 1. 状态转换请求
```go
// 用户调用状态转换接口
POST /agency/endpoint-applications/{id}/transition
{
  "action": "approve",
  "data": {
    "remark": "审核通过"
  }
}
```

### 2. Service层处理
```go
func (e endpointApplyService) TransitionState(c *gin.Context, req *api.StateTransitionReq) error {
    // 构建状态转换请求
    stateReq := &statemachine.StateTransitionRequest{
        ApplicationID: req.ID,
        CurrentState:  consts.EndpointApplicationState(apply.State),
        Action:        req.Action,
        Data:          req.Data,
    }
    
    // 添加事务DAO和Service到数据中，供处理器使用
    stateReq.Data["txRepo"] = txRepo
    stateReq.Data["service"] = e
    
    // 执行状态机转换（会自动调用配置的处理器）
    return stateMachine.ExecuteTransition(c, stateReq)
}
```

### 3. 状态机自动调用处理器
```go
// 状态机内部逻辑
func (sm *EndpointApplicationStateMachine) executeStateChange(ctx *gin.Context, node *StateNode, req *StateTransitionRequest) error {
    updateData := map[string]any{
        "state": int(req.TargetState),
    }

    // 执行配置的处理器
    for _, processorName := range node.Processors {
        if processor, exists := sm.processors[processorName]; exists {
            if err := processor.Process(ctx, req, updateData); err != nil {
                return err
            }
        }
    }

    return nil
}
```

## 处理器架构

### 1. StateProcessor（通用状态处理器）
```go
type StateProcessor struct {
    service endpointApplyService
}

func (p *StateProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    // 获取事务DAO
    txRepo, _ := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
    
    // 设置基本状态信息
    updateData["state"] = int(req.TargetState)
    updateData["updated_at"] = time.Now()
    
    // 更新数据库
    return txRepo.UpdateEndpointApply(ctx, int(req.ApplicationID), updateData)
}
```

### 2. AuditProcessor（审核处理器）
```go
type AuditProcessor struct {
    service endpointApplyService
}

func (p *AuditProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    switch req.Action {
    case "approve":
        updateData["audit_time"] = time.Now()
        updateData["audit_status"] = 1
        // 设置审核人信息...
    case "reject":
        updateData["audit_time"] = time.Now()
        updateData["audit_status"] = -1
        // 处理拒绝逻辑...
    }
    return nil
}
```

### 3. PostbackProcessor（回传处理器）
```go
type PostbackProcessor struct {
    service endpointApplyService
}

func (p *PostbackProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    switch req.Action {
    case "complete_store":
        // 处理回传数据
        updateData["postback_time"] = time.Now()
        updateData["postback_status"] = 1
        
        // 保存回传数据到专门的表
        // ...
    }
    return nil
}
```

## 配置示例

### 标准工作流配置
```json
{
  "name": "标准建店工作流",
  "validators": ["permission", "data"],
  "processors": ["state", "audit", "postback"],
  "states": {
    "0": {
      "id": 0,
      "name": "待审核",
      "processors": ["state", "audit"],
      "actions": [
        {
          "type": "approve",
          "label": "审核通过",
          "config": {"next_state": 100}
        }
      ]
    },
    "100": {
      "id": 100,
      "name": "审核通过",
      "processors": ["state", "postback"],
      "actions": [
        {
          "type": "complete_store",
          "label": "回传建店资料",
          "config": {"next_state": 500}
        }
      ]
    }
  }
}
```

## 处理器执行顺序

### 1. 审核通过操作
```
用户操作: approve
当前状态: 0 (待审核)
配置的处理器: ["state", "audit"]

执行顺序:
1. StateProcessor.Process() - 更新基本状态信息
2. AuditProcessor.Process() - 处理审核逻辑

最终结果:
- 状态更新为 100
- 审核时间、审核人、审核状态等字段更新
- 数据库事务提交
```

### 2. 建店回传操作
```
用户操作: complete_store
当前状态: 100 (审核通过)
配置的处理器: ["state", "postback"]

执行顺序:
1. StateProcessor.Process() - 更新基本状态信息
2. PostbackProcessor.Process() - 处理回传数据

最终结果:
- 状态更新为 500
- 回传时间、回传状态等字段更新
- 回传数据保存到专门的表
- 数据库事务提交
```

## 优势

### 1. 配置驱动
- 不同的状态可以配置不同的处理器组合
- 修改业务逻辑只需要调整配置，无需修改代码

### 2. 职责分离
- StateProcessor: 负责通用的状态更新
- AuditProcessor: 负责审核相关的业务逻辑
- PostbackProcessor: 负责回传相关的业务逻辑

### 3. 易于扩展
```go
// 添加新的处理器
type NotificationProcessor struct {
    service endpointApplyService
}

func (p *NotificationProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    // 发送通知逻辑
    return nil
}

// 在GetProcessor中添加
case "notification":
    return &NotificationProcessor{service: e}, nil
```

### 4. 易于测试
- 每个处理器可以独立测试
- 可以Mock特定的处理器
- 支持集成测试

## 总结

新的架构实现了：
1. **所有业务逻辑都在处理器中实现**
2. **状态机根据配置自动调用处理器**
3. **处理器可以访问事务DAO和Service的所有功能**
4. **配置决定业务流程，代码提供业务能力**

这样的设计完全符合您的期望：配置什么处理器就执行什么业务逻辑，状态机自动调用，无需手动处理。
