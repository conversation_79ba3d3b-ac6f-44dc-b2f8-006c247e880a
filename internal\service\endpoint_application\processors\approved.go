package processors

import (
	"strconv"

	endpointDao "marketing/internal/dao/endpoint"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// ApprovedProcessor 审核通过状态处理器
type ApprovedProcessor struct{}

// NewApprovedProcessor 创建审核通过处理器
func NewApprovedProcessor() *ApprovedProcessor {
	return &ApprovedProcessor{}
}

// Process 处理审核通过状态 - 创建终端
func (p *ApprovedProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}

	// 获取申请信息
	apply, exists := req.Data["application"]
	if !exists {
		return errors.NewErr("缺少申请信息")
	}

	applyModel, ok := apply.(*model.EndpointApplication)
	if !ok {
		return errors.NewErr("申请信息格式错误")
	}

	// 创建终端信息
	endpointInfo, err := p.createEndpoint(ctx, txRepo, applyModel)
	if err != nil {
		return err
	}

	if endpointInfo != nil {
		// 将终端信息保存到请求数据中，供后续处理器使用
		req.Data["endpoint"] = endpointInfo
		req.Data["endpoint_code"] = endpointInfo.Code

		// 立即更新申请记录，关联创建的终端
		updateData["add_to_endpoint_id"] = endpointInfo.ID
	}

	return nil
}

// createEndpoint 创建终端信息
func (p *ApprovedProcessor) createEndpoint(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, apply *model.EndpointApplication) (*model.Endpoint, error) {
	// 构建完整地址
	address := apply.Address
	if apply.Province > 0 {
		provinceStr := strconv.Itoa(apply.Province)
		cityStr := strconv.Itoa(apply.City)
		districtStr := strconv.Itoa(apply.District)
		address = provinceStr + cityStr + districtStr + apply.Address
	}

	// 获取事务数据库连接
	txDB := txRepo.GetDB(ctx)
	txEndpointDao := endpointDao.NewEndpointDao(txDB)

	// 创建终端数据
	endpointInfo, err := txEndpointDao.CreateEndpoint(ctx, &model.Endpoint{
		// 必填字段
		Name:      apply.Name,
		Type:      int8(apply.Type),
		Province:  apply.Province,
		City:      apply.City,
		District:  apply.District,
		Address:   address,
		TopAgency: int(apply.TopAgency),
		Phone:     apply.Phone,
		Manager:   &apply.Manager,
		Blng:      &apply.Blng,
		Blat:      &apply.Blat,
		Lng:       apply.Lng,
		Lat:       apply.Lat,

		// 可选字段
		SecondAgency: int(apply.SecondAgency),
		ChannelLevel: apply.ChannelLevel,
	})

	return endpointInfo, err
}

// GetName 获取处理器名称
func (p *ApprovedProcessor) GetName() string {
	return "approved"
}
