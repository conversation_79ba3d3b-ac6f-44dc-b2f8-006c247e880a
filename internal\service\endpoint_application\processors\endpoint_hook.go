package processors

import (
	"marketing/internal/model"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// EndpointHookProcessor 终端钩子处理器
type EndpointHookProcessor struct{}

// NewEndpointHookProcessor 创建终端钩子处理器
func NewEndpointHookProcessor() *EndpointHookProcessor {
	return &EndpointHookProcessor{}
}

// Process 处理终端相关的钩子逻辑
func (p *EndpointHookProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取服务实例
	service, exists := req.Data["service"]
	if !exists {
		return nil // 如果没有服务实例，跳过处理
	}

	// 获取终端信息和终端代码
	endpoint, _ := req.Data["endpoint"].(*model.Endpoint)

	// 终端创建/更新后的钩子
	if endpoint != nil {
		// 调用服务的EndpointCreated方法（终端创建后的业务处理）
		if serviceWithEndpointCreated, ok := service.(interface {
			EndpointCreated(*gin.Context, *model.Endpoint)
		}); ok {
			serviceWithEndpointCreated.EndpointCreated(ctx, endpoint)
		}
	}

	return nil
}

// GetName 获取处理器名称
func (p *EndpointHookProcessor) GetName() string {
	return "endpoint_hook"
}
