# 根据申请ID获取分期列表接口

## 接口概述
根据申请ID获取该申请下的所有分期记录，不进行分页，返回完整列表。

### 请求信息
- **请求URL**: `/admin/endpoint-application/installments/by-application`
- **请求方式**: `GET`

### 请求参数
| 参数名 | 必选 | 类型 | 说明 |
|:------|:-----|:-----|:-----|
| application_id | 是 | uint | 申请ID |

### 请求示例
```
GET /admin/endpoint-application/installments/by-application?application_id=123
```

### 响应数据结构

#### 成功响应示例
```json
{
  "ok": 1,
  "msg": "ok",
  "data": [
    {
      "id": 1,
      "application_id": 123,
      "application_name": "读书郎专卖店(测试店)",
      "year": 2025,
      "month": 1,
      "date": "2025-01-01T00:00:00Z",
      "fee": 500000,
      "staff_fee": 100000,
      "mission_score": 85,
      "rent_proof": "rbcare/proofs/rent_proof_001.jpg",
      "rent_proof_audit_state": 1,
      "rent_proof_audit_remark": "凭证清晰，审核通过",
      "rent_proof_updated_at": "2025-01-15T10:30:00Z",
      "support_status": 1,
      "staff_support_status": 1,
      "staff_record_remark": "按时入账",
      "recorded_by": 101,
      "recorded_at": "2025-01-15T15:20:00Z",
      "record_remark": "正常入账",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-15T15:20:00Z",
      "support_status_name": "已入账",
      "recorded_by_name": "财务张三"
    },
    {
      "id": 2,
      "application_id": 123,
      "application_name": "读书郎专卖店(测试店)",
      "year": 2025,
      "month": 2,
      "date": "2025-02-01T00:00:00Z",
      "fee": 500000,
      "staff_fee": 100000,
      "mission_score": null,
      "rent_proof": "",
      "rent_proof_audit_state": 0,
      "rent_proof_audit_remark": "",
      "rent_proof_updated_at": null,
      "support_status": 0,
      "staff_support_status": 0,
      "staff_record_remark": "",
      "recorded_by": 0,
      "recorded_at": null,
      "record_remark": "",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z",
      "support_status_name": "未入账",
      "recorded_by_name": ""
    }
  ]
}
```

#### 空数据响应示例
```json
{
  "ok": 1,
  "msg": "ok",
  "data": []
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| id | uint | 分期记录ID |
| application_id | uint | 申请ID |
| application_name | string | 申请名称（终端名称） |
| year | uint16 | 年份 |
| month | uint8 | 月份 |
| date | string | 分期日期 |
| fee | uint | 支持金额（分） |
| staff_fee | uint | 人员支持金额（分），可为null |
| mission_score | uint | 任务考核分数，可为null |
| rent_proof | string | 租金凭证图片路径 |
| rent_proof_audit_state | uint8 | 租金凭证审核状态（0:未审核,1:通过,2:不通过） |
| rent_proof_audit_remark | string | 租金凭证审核备注 |
| rent_proof_updated_at | string | 凭证上传时间，可为null |
| support_status | uint8 | 入账状态（0:未入账,1:已入账,2:中止） |
| staff_support_status | uint8 | 支持人员入账状态（0:未入账,1:已入账,2:中止） |
| staff_record_remark | string | 支持人员入账备注 |
| recorded_by | uint | 入账人ID |
| recorded_at | string | 入账时间，可为null |
| record_remark | string | 入账备注 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| support_status_name | string | 入账状态名称 |
| recorded_by_name | string | 入账人姓名 |

### 错误响应
```json
{
  "ok": 0,
  "msg": "application_id参数不能为空",
  "data": null
}
```

### 状态码说明
- `200`: 请求成功
- `400`: 参数错误
- `500`: 服务器内部错误

### 与原接口的区别

| 对比项 | 原接口 (`/installments`) | 新接口 (`/installments/by-application`) |
|:------|:------------------------|:--------------------------------------|
| 分页 | 支持分页 | 不分页，返回全部数据 |
| 必填参数 | application_id 可选 | application_id 必填 |
| 查询条件 | 支持年份、月份、状态筛选 | 只根据申请ID筛选 |
| 使用场景 | 管理员查看分期列表 | 查看特定申请的所有分期 |
| 性能 | 适合大量数据展示 | 适合单个申请的完整数据 |

### 使用场景
1. **申请详情页面**：显示该申请下的所有分期记录
2. **财务核算**：查看特定申请的完整分期情况
3. **数据导出**：获取完整的分期数据用于报表生成
4. **状态概览**：快速了解申请的分期执行情况

### 注意事项
1. 该接口不进行分页，如果分期数量很大可能影响性能
2. 如果申请不存在分期记录，返回空数组而不是错误
3. 申请名称获取失败时，该字段为空但不影响其他数据
4. 入账人姓名获取失败时，该字段为空但不影响其他数据
