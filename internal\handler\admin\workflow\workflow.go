package workflow

import (
	workflowApi "marketing/internal/api/workflow"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	workflowSvc "marketing/internal/service/workflow"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// WorkflowHandler 通用工作流Handler接口
type WorkflowHandler interface {
	// ExecuteWorkflow 执行工作流
	ExecuteWorkflow(c *gin.Context)

	// GetWorkflowState 获取工作流状态
	GetWorkflowState(c *gin.Context)

	// GetAvailableActions 获取可用操作
	GetAvailableActions(c *gin.Context)
}

// workflowHandler 工作流Handler实现
type workflowHandler struct {
	workflowSvc workflowSvc.WorkflowService
}

// NewWorkflowHandler 创建工作流Handler
func NewWorkflowHandler(workflowSvc workflowSvc.WorkflowService) WorkflowHandler {
	return &workflowHandler{
		workflowSvc: workflowSvc,
	}
}

// ExecuteWorkflow 执行工作流
func (h *workflowHandler) ExecuteWorkflow(c *gin.Context) {
	var req workflowApi.WorkflowExecuteReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	resp, err := h.workflowSvc.ExecuteWorkflow(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetWorkflowState 获取工作流状态
func (h *workflowHandler) GetWorkflowState(c *gin.Context) {
	var req workflowApi.GetWorkflowStateReq
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, err)
		return
	}

	resp, err := h.workflowSvc.GetWorkflowState(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetAvailableActions 获取可用操作
func (h *workflowHandler) GetAvailableActions(c *gin.Context) {
	workflowType := c.Query("workflow_type")
	if workflowType == "" {
		handler.Error(c, errors.NewErr("workflow_type参数不能为空"))
		return
	}

	entityIDStr := c.Query("entity_id")
	if entityIDStr == "" {
		handler.Error(c, errors.NewErr("entity_id参数不能为空"))
		return
	}

	entityID := cast.ToUint(entityIDStr)
	if entityID == 0 {
		handler.Error(c, errors.NewErr("entity_id参数格式错误"))
		return
	}

	actions, err := h.workflowSvc.GetAvailableActions(c, workflowType, entityID)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, map[string]interface{}{
		"workflow_type": workflowType,
		"entity_id":     entityID,
		"actions":       actions,
	})
}
