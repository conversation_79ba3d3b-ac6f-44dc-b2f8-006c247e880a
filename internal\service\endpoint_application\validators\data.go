package validators

import (
	"fmt"

	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// DataValidator 数据验证器
type DataValidator struct{}

// NewDataValidator 创建数据验证器
func NewDataValidator() *DataValidator {
	return &DataValidator{}
}

// Validate 验证请求数据
func (v *DataValidator) Validate(ctx *gin.Context, req *statemachine.StateTransitionRequest) error {
	// 根据操作类型验证必需的数据
	switch req.Action {
	case "reject":
		// 拒绝操作必须有备注
		if remark, exists := req.Data["remark"]; !exists || remark == "" {
			return fmt.Errorf("拒绝操作必须提供备注说明")
		}
		
	case "complete_store":
		// 建店回传必须有必要的文件
		requiredFields := []string{"write_off_table", "lease_contract"}
		for _, field := range requiredFields {
			if value, exists := req.Data[field]; !exists || value == "" {
				return fmt.Errorf("建店回传缺少必需字段: %s", field)
			}
		}
		
		// 验证年租金
		if annualRent, exists := req.Data["annual_rent"]; exists {
			if rent, ok := annualRent.(float64); ok && rent <= 0 {
				return fmt.Errorf("年租金必须大于0")
			}
		}
		
	case "submit_material":
		// 物料申请必须有物料列表
		if materialList, exists := req.Data["material_list"]; !exists || materialList == nil {
			return fmt.Errorf("物料申请必须提供物料列表")
		}
		
	case "submit_writeoff":
		// 核销申请必须有金额和凭证
		if amount, exists := req.Data["writeoff_amount"]; !exists || amount == nil {
			return fmt.Errorf("核销申请必须提供核销金额")
		}
		
		if voucher, exists := req.Data["writeoff_voucher"]; !exists || voucher == "" {
			return fmt.Errorf("核销申请必须提供核销凭证")
		}
	}
	
	return nil
}

// GetName 获取验证器名称
func (v *DataValidator) GetName() string {
	return "data"
}
