package endpoint_application

import (
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/consts"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
	applySvc "marketing/internal/service/endpoint_application"
	"slices"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ApplyHandler interface {
	GetList(c *gin.Context)
	GetEndpointApplyDetail(c *gin.Context)
	AuditEndpointApply(c *gin.Context)
	GetMaterials(c *gin.Context)
	MaterialSupport(c *gin.Context)
	WriteOff(c *gin.Context)
	Terminate(c *gin.Context)
	ChannelConfirmation(c *gin.Context)
	RecordConfirmation(c *gin.Context)
	LatestEndpointImage(c *gin.Context)
	SalesAmount(c *gin.Context)
	// GetAvailableActions 状态机相关接口
	GetAvailableActions(c *gin.Context)
	GetNextStates(c *gin.Context)
	TransitionState(c *gin.Context)

	// RecordAccount 入账相关接口
	RecordAccount(c *gin.Context)                // 入账操作
	GetInstallmentList(c *gin.Context)           // 获取分期列表
	GetInstallmentByID(c *gin.Context)           // 获取分期详情
	GetInstallmentsByApplication(c *gin.Context) // 根据申请ID获取分期列表（不分页）
}

type applyHandler struct {
	svc            applySvc.EndpointApplyService
	installmentSvc applySvc.InstallmentService
}

func NewApplyHandler(svc applySvc.EndpointApplyService, installmentSvc applySvc.InstallmentService) ApplyHandler {
	return &applyHandler{
		svc:            svc,
		installmentSvc: installmentSvc,
	}
}

func (h *applyHandler) GetList(c *gin.Context) {
	var req api.EndpointApplicationListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()

	data, total, err := h.svc.GetEndpointApplyList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data":  data,
		"total": total,
	})
}

// GetEndpointApplyDetail 获取申请详情
func (h *applyHandler) GetEndpointApplyDetail(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}

	detail, err := h.svc.GetEndpointApplyDetail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, detail)
}

func (h *applyHandler) AuditEndpointApply(c *gin.Context) {
	var req api.AuditApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id is required"))
		return
	}
	req.ID = id
	if err := h.svc.AuditApply(c, &req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) GetMaterials(c *gin.Context) {
	name := c.Query("name")
	page := c.Query("page")
	pageSize := c.Query("page_size")
	pageInt := cast.ToInt(page)
	pageSizeInt := cast.ToInt(pageSize)
	if pageInt == 0 {
		pageInt = 1
	}
	if pageSizeInt == 0 {
		pageSizeInt = 20
	}
	//获取所有的分类
	categories := []int{17, 2}
	categoriesSvc := service.NewMaterialCategoryService(dao.NewMaterialDao(), dao.NewMaterialCategoryDao())
	allCategories, err := categoriesSvc.GetMaterialAndChildren(c, categories)
	if err != nil {
		handler.Error(c, err)
		return
	}
	data, err := h.svc.GetMaterials(c, name, allCategories, pageInt, pageSizeInt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// GetAvailableActions 获取当前状态可执行的操作
func (h *applyHandler) GetAvailableActions(c *gin.Context) {
	applicationID := cast.ToUint(c.Param("id"))
	if applicationID == 0 {
		handler.Error(c, errors.NewErr("ID参数错误"))
		return
	}

	actions, err := h.svc.GetAvailableActions(c, applicationID)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, actions)
}

// GetNextStates 获取当前状态的可能下一状态
func (h *applyHandler) GetNextStates(c *gin.Context) {
	applicationID := cast.ToUint(c.Param("id"))
	if applicationID == 0 {
		handler.Error(c, errors.NewErr("ID参数错误"))
		return
	}

	states, err := h.svc.GetNextStates(c, applicationID)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 转换为字符串数组，方便前端使用
	handler.Success(c, gin.H{"next_states": states})
}

func (h *applyHandler) MaterialSupport(c *gin.Context) {
	var req []*api.EndpointMaterialSupport
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	if len(req) == 0 {
		handler.Error(c, errors.NewErr("请选择物料"))
		return
	}
	err := h.svc.MaterialSupport(c, id, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) WriteOff(c *gin.Context) {
	var req api.WriteOffReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	if !slices.Contains([]consts.EndpointApplicationState{consts.ApplicationInitialWriteOff, consts.ApplicationInitialWriteOffRejected}, consts.EndpointApplicationState(req.State)) {
		handler.Error(c, errors.NewErr("状态参数错误"))
		return
	}
	req.ID = id
	err := h.svc.WriteOff(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// Terminate 建店终止 - 可在任何状态下执行，不受工作流限制
func (h *applyHandler) Terminate(c *gin.Context) {
	var req api.TerminateReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.ID = id
	// 终止操作绕过工作流限制，可在任何状态下执行
	err := h.svc.Terminate(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) ChannelConfirmation(c *gin.Context) {
	var req api.ChannelAuditReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.ID = id
	if !slices.Contains([]consts.EndpointApplicationState{consts.ApplicationChannelConfirmation, consts.ApplicationChannelConfirmationRejected}, consts.EndpointApplicationState(req.State)) {
		handler.Error(c, errors.NewErr("状态参数错误"))
		return
	}
	err := h.svc.ChannelConfirmation(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) RecordConfirmation(c *gin.Context) {
	var req api.AuditApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.ID = id
	if !slices.Contains([]consts.EndpointApplicationState{consts.ApplicationRecordConfirmation}, consts.EndpointApplicationState(req.State)) {
		handler.Error(c, errors.NewErr("状态参数错误"))
		return
	}
	err := h.svc.RecordConfirmation(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) LatestEndpointImage(c *gin.Context) {
	id := cast.ToInt(c.Query("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	image, err := h.svc.LatestEndpointImage(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, image)
}

func (h *applyHandler) SalesAmount(c *gin.Context) {
	id := cast.ToInt(c.Query("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	years := cast.ToInt(c.Query("years"))
	if years == 0 {
		years = 1
	}
	endAt := cast.ToInt64(c.Query("end_at"))
	if endAt == 0 {
		endAt = time.Now().Unix()
	}

	count, amount, err := h.svc.SalesAmount(c, id, years, endAt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"count":  count,
		"amount": amount,
	})
}

// TransitionState 状态转换
func (h *applyHandler) TransitionState(c *gin.Context) {
	var req api.StateTransitionReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("ID参数错误"))
		return
	}

	if err := h.svc.TransitionState(c, &req); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// RecordAccount 入账操作
func (h *applyHandler) RecordAccount(c *gin.Context) {
	var req api.AccountRecordReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.ID = id

	if err := h.installmentSvc.RecordAccount(c, &req); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// GetInstallmentList 获取分期列表
func (h *applyHandler) GetInstallmentList(c *gin.Context) {
	var req api.InstallmentListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	data, total, err := h.installmentSvc.GetInstallmentList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, data, total)
}

// GetInstallmentByID 获取分期详情
func (h *applyHandler) GetInstallmentByID(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}

	data, err := h.installmentSvc.GetInstallmentByID(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, data)
}

// GetInstallmentsByApplication 根据申请ID获取分期列表（不分页）
func (h *applyHandler) GetInstallmentsByApplication(c *gin.Context) {
	var req api.InstallmentsByApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	data, err := h.installmentSvc.GetInstallmentsByApplication(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, data)
}
