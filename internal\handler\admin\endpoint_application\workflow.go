package endpoint_application

import (
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	applySvc "marketing/internal/service/endpoint_application"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// WorkflowHandler 工作流管理Handler接口
type WorkflowHandler interface {
	GetWorkflowTemplates(c *gin.Context)
	CreateWorkflowTemplate(c *gin.Context)
	UpdateWorkflowTemplate(c *gin.Context)
	DeleteWorkflowTemplate(c *gin.Context)
	GetWorkflowTemplate(c *gin.Context)
}

// workflowHandler 工作流管理Handler实现
type workflowHandler struct {
	workflowSvc applySvc.WorkflowService
}

// NewWorkflowHandler 创建工作流管理Handler
func NewWorkflowHandler(workflowSvc applySvc.WorkflowService) WorkflowHandler {
	return &workflowHandler{
		workflowSvc: workflowSvc,
	}
}

// GetWorkflowTemplates 获取工作流模板列表
func (h *workflowHandler) GetWorkflowTemplates(c *gin.Context) {
	templates, err := h.workflowSvc.GetWorkflowTemplates(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, templates)
}

// CreateWorkflowTemplate 创建工作流模板
func (h *workflowHandler) CreateWorkflowTemplate(c *gin.Context) {
	var req api.CreateWorkflowTemplateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	if err := h.workflowSvc.CreateWorkflowTemplate(c, &req); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// UpdateWorkflowTemplate 更新工作流模板
func (h *workflowHandler) UpdateWorkflowTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		handler.Error(c, errors.NewErr("模板ID参数错误"))
		return
	}

	var req api.UpdateWorkflowTemplateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	if err := h.workflowSvc.UpdateWorkflowTemplate(c, cast.ToInt(templateID), &req); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// DeleteWorkflowTemplate 删除工作流模板
func (h *workflowHandler) DeleteWorkflowTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		handler.Error(c, errors.NewErr("模板ID参数错误"))
		return
	}

	if err := h.workflowSvc.DeleteWorkflowTemplate(c, cast.ToInt(templateID)); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// GetWorkflowTemplate 获取单个工作流模板
func (h *workflowHandler) GetWorkflowTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		handler.Error(c, errors.NewErr("模板ID参数错误"))
		return
	}

	// 这里需要在服务层添加GetWorkflowTemplate方法
	template, err := h.workflowSvc.GetWorkflowTemplate(c, cast.ToInt(templateID))
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, template)
}
