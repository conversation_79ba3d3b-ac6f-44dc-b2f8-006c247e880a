package endpoint_application

import (
	"marketing/internal/pkg/statemachine"
	"strconv"
)

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	State      int    `json:"state"`
	Name       string `json:"name"`
	Order      int    `json:"order"`
	Status     string `json:"status"`      // completed, rejected, current, waiting
	StatusText string `json:"status_text"` // "审核通过", "已完成", "核销不通过"等
}

// buildWorkflowSteps 根据当前状态构建工作流步骤
func (e endpointApplyService) buildWorkflowSteps(currentState int, workflow *statemachine.WorkflowConfig) []*WorkflowStep {
	// 从工作流配置构建主流程路径
	mainPath := e.buildMainPath(workflow)

	var steps []*WorkflowStep
	for i, stepState := range mainPath {
		stateKey := strconv.Itoa(stepState)
		stateNode, exists := workflow.States[stateKey]
		if !exists {
			continue
		}

		step := &WorkflowStep{
			State:      stepState,
			Name:       stateNode.Name,
			Order:      i + 1,
			Status:     e.getStepStatus(stepState, currentState),
			StatusText: e.getStatusText(stepState, currentState),
		}
		steps = append(steps, step)
	}

	return steps
}

// buildMainPath 构建主流程路径
func (e endpointApplyService) buildMainPath(workflow *statemachine.WorkflowConfig) []int {
	mainPath := []int{}
	visited := make(map[int]bool)
	currentState := int(workflow.StartState)

	for !visited[currentState] && len(mainPath) < 10 {
		mainPath = append(mainPath, currentState)
		visited[currentState] = true

		stateKey := strconv.Itoa(currentState)
		if stateNode, exists := workflow.States[stateKey]; exists {
			// 找下一个正向状态（优先approve操作）
			nextState := -1
			for _, action := range []string{"approve", "submit_material", "postback"} {
				if next, ok := stateNode.Transitions[action]; ok && int(next) > 0 {
					nextState = int(next)
					break
				}
			}
			if nextState == -1 || visited[nextState] {
				break
			}
			currentState = nextState
		} else {
			break
		}
	}

	return mainPath
}

// getStepStatus 获取步骤状态
func (e endpointApplyService) getStepStatus(stepState, currentState int) string {
	switch {
	case currentState == stepState:
		return "current"
	case e.isStepCompleted(stepState, currentState):
		return "completed"
	case e.isStepRejected(stepState, currentState):
		return "rejected"
	default:
		return "waiting"
	}
}

// isStepCompleted 判断步骤是否已完成
func (e endpointApplyService) isStepCompleted(stepState, currentState int) bool {
	if currentState < 0 {
		// 负数状态：比如-400说明到过300但在400被拒绝
		absCurrentState := -currentState
		return stepState < absCurrentState
	}
	// 正常状态：当前状态>=步骤状态就是完成了
	return currentState >= stepState
}

// isStepRejected 判断步骤是否被拒绝
func (e endpointApplyService) isStepRejected(stepState, currentState int) bool {
	switch stepState {
	case 100:
		return currentState == -100
	case 400:
		return currentState == -400
	default:
		return false
	}
}

// getStatusText 获取状态文本
func (e endpointApplyService) getStatusText(stepState, currentState int) string {
	status := e.getStepStatus(stepState, currentState)

	switch status {
	case "completed":
		if stepState == 0 {
			return "审核通过"
		}
		return "已完成"
	case "rejected":
		if stepState == 100 {
			return "审核不通过"
		} else if stepState == 400 {
			return "核销不通过"
		}
		return "审核不通过"
	case "current":
		return "进行中"
	default:
		return "等待中"
	}
}
