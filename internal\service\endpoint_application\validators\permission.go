package validators

import (
	"fmt"

	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// PermissionValidator 权限验证器
type PermissionValidator struct{}

// NewPermissionValidator 创建权限验证器
func NewPermissionValidator() *PermissionValidator {
	return &PermissionValidator{}
}

// Validate 验证用户权限
func (v *PermissionValidator) Validate(ctx *gin.Context, req *statemachine.StateTransitionRequest) error {
	// 获取当前用户信息
	userID := ctx.GetUint("uid")
	if userID == 0 {
		return fmt.Errorf("用户未登录")
	}
	
	systemType := ctx.GetString("system_type")
	
	// 根据操作类型验证权限
	switch req.Action {
	case "approve", "reject":
		// 审核操作需要管理员权限
		if systemType != "admin" {
			return fmt.Errorf("只有管理员可以执行审核操作")
		}
	case "complete_store":
		// 建店回传可以由代理商执行
		if systemType != "agency" && systemType != "admin" {
			return fmt.Errorf("只有代理商或管理员可以回传建店资料")
		}
	case "submit_material":
		// 物料申请可以由代理商执行
		if systemType != "agency" && systemType != "admin" {
			return fmt.Errorf("只有代理商或管理员可以申请物料")
		}
	case "submit_writeoff":
		// 核销申请可以由代理商执行
		if systemType != "agency" && systemType != "admin" {
			return fmt.Errorf("只有代理商或管理员可以提交核销申请")
		}
	case "channel_confirmation":
		// 渠道确认需要管理员权限
		if systemType != "admin" {
			return fmt.Errorf("只有管理员可以执行渠道确认")
		}
	}
	
	return nil
}

// GetName 获取验证器名称
func (v *PermissionValidator) GetName() string {
	return "permission"
}
