package renew

import (
	"marketing/internal/api/renew"
	"marketing/internal/consts"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ApplicationDao interface {
	Add(c *gin.Context, req *renew.AddApplicationReq) error
	Get(c *gin.Context, id uint) (*model.RenewApplication, error)
	GetStatus(c *gin.Context, id uint) ([]*model.RenewApplicationStatus, error)
	GetStatusBatch(c *gin.Context, ids []uint) (map[uint][]*model.RenewApplicationStatus, error)
	Update(c *gin.Context, req *renew.AddApplicationReq) error
	UpdateStatus(c *gin.Context, id, handlerID uint, status, comment string) error
	Delete(c *gin.Context, id uint) error
	Audit(c *gin.Context, req *renew.AuditApplicationReq) error
	Check(c *gin.Context, req *renew.CheckMachineReq) error
	Lists(c *gin.Context, req *renew.ListApplicationReq) ([]*model.RenewApplication, int64, error)
	GetByBarcode(c *gin.Context, barcode string) (*model.RenewApplication, error)
	GetByBarcodes(c *gin.Context, barcode []string) ([]*model.RenewApplication, error)
	Completed(c *gin.Context, id []uint, handlerID uint) error
}

type application struct {
	db *gorm.DB
}

func NewApplicationDao(db *gorm.DB) ApplicationDao {
	return &application{
		db: db,
	}
}

// Add 新增
func (a *application) Add(c *gin.Context, req *renew.AddApplicationReq) error {
	var data model.RenewApplication
	data.Barcode = req.Barcode
	data.Model = req.Model
	data.ModelID = req.ModelID
	data.TopAgency = req.TopAgency
	data.SecondAgency = req.SecondAgency
	data.EndpointID = req.EndpointID
	data.Issues = strings.Join(req.Issues, ",")
	data.DamageDescription = req.DamageDescription
	data.DamageImages = req.DamageImages
	data.Status = req.Status
	data.Applicant = req.Applicant
	data.ApplicantType = req.ApplicantType
	err := a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&data).Error
		return err
	})
	return err
}

func (a *application) Get(c *gin.Context, id uint) (*model.RenewApplication, error) {
	var data model.RenewApplication
	err := a.db.WithContext(c).Where("id = ?", id).First(&data).Error
	return &data, err
}

func (a *application) GetStatus(c *gin.Context, id uint) ([]*model.RenewApplicationStatus, error) {
	var data []*model.RenewApplicationStatus
	err := a.db.WithContext(c).Where("application_id = ?", id).Order("id desc").Find(&data).Error
	return data, err
}

func (a *application) GetStatusBatch(c *gin.Context, ids []uint) (map[uint][]*model.RenewApplicationStatus, error) {
	var statusLogs []*model.RenewApplicationStatus
	result := make(map[uint][]*model.RenewApplicationStatus)

	if len(ids) == 0 {
		return result, nil
	}

	// Query all status logs for the given application IDs in a single query
	err := a.db.WithContext(c).
		Where("application_id IN ?", ids).
		Order("id desc").
		Find(&statusLogs).Error

	if err != nil {
		return nil, err
	}

	// Group status logs by application ID
	for _, log := range statusLogs {
		result[log.ApplicationID] = append(result[log.ApplicationID], log)
	}

	return result, nil
}

func (a *application) Update(c *gin.Context, req *renew.AddApplicationReq) error {
	if req.ID == 0 {
		return nil
	}
	var data model.RenewApplication
	data.Barcode = req.Barcode
	data.Model = req.Model
	data.ModelID = req.ModelID
	data.TopAgency = req.TopAgency
	data.SecondAgency = req.SecondAgency
	data.EndpointID = req.EndpointID
	data.Issues = strings.Join(req.Issues, ",")
	data.DamageDescription = req.DamageDescription
	data.DamageImages = req.DamageImages
	data.Status = req.Status
	data.Applicant = req.Applicant
	err := a.db.WithContext(c).Model(&model.RenewApplication{}).Where("id = ?", req.ID).Updates(&data).Error
	return err
}

func (a *application) UpdateStatus(c *gin.Context, id, handlerID uint, status, comment string) error {
	var data model.RenewApplicationStatus
	data.ApplicationID = id
	data.Status = status
	data.HandlerID = handlerID
	data.Comment = comment
	err := a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&data).Error
		return err
	})
	return err
}

func (a *application) Delete(c *gin.Context, id uint) error {
	err := a.db.WithContext(c).Where("id = ?", id).Delete(&model.RenewApplication{}).Error
	return err
}

func (a *application) Audit(c *gin.Context, req *renew.AuditApplicationReq) error {
	var data model.RenewApplication
	data.Status = req.Status
	err := a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&model.RenewApplication{}).Where("id =?", req.ID).Updates(&data).Error
		if err != nil {
			return err
		}
		err = a.UpdateStatus(c, req.ID, req.HandlerID, req.Status, req.Comment)
		return err
	})
	return err
}

func (a *application) Check(c *gin.Context, req *renew.CheckMachineReq) error {
	var data model.RenewApplication
	data.Status = req.Status
	data.CheckResult = req.CheckResult
	data.CheckFacade = req.CheckFacade
	data.CheckImages = types.JSONStringArray(req.CheckImages)
	err := a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&model.RenewApplication{}).Where("id =?", req.ID).Updates(&data).Error
		if err != nil {
			return err
		}
		err = a.UpdateStatus(c, req.ID, req.HandlerID, req.Status, req.Comment)
		return err
	})
	return err
}

func (a *application) Lists(c *gin.Context, req *renew.ListApplicationReq) ([]*model.RenewApplication, int64, error) {
	var data []*model.RenewApplication
	var total int64
	query := a.db.WithContext(c).Model(&model.RenewApplication{})
	if req.Barcode != "" {
		query = query.Where("barcode = ?", strings.TrimSpace(req.Barcode))
	}
	if len(req.Barcodes) > 0 {
		barcodes := req.Barcodes
		for i := range barcodes {
			barcodes[i] = strings.TrimSpace(barcodes[i])
		}
		query = query.Where("barcode in ?", barcodes)
	}
	if req.Model != "" {
		query = query.Where("model = ?", req.Model)
	}
	if req.Status != "" {
		query = query.Where("status =?", req.Status)
	}
	if len(req.StatusSlices) > 0 {
		query = query.Where("status in (?)", req.StatusSlices)
	}
	// 经销商审核列表
	if req.AgencyID > 0 {
		query = query.Where("top_agency =? or second_agency =?", req.AgencyID, req.AgencyID)
	}
	if req.TopAgency > 0 {
		query = query.Where("top_agency =?", req.TopAgency)
	}
	if req.SecondAgency > 0 {
		query = query.Where("second_agency =?", req.SecondAgency)
	}
	// 经销商申请列表
	if req.Applicant > 0 && req.DataType != 1 {
		query = query.Where("applicant =?", req.Applicant)
	}
	if req.EndpointID > 0 {
		query = query.Where("endpoint_id =?", req.EndpointID)
	}
	if req.StartTime != "" {
		query = query.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("created_at <=?", req.EndTime)
	}
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	query = query.Order("id desc").Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize)
	err = query.Find(&data).Error
	return data, total, err
}

func (a *application) GetByBarcode(c *gin.Context, barcode string) (*model.RenewApplication, error) {
	var data model.RenewApplication
	err := a.db.WithContext(c).Where("barcode = ?", barcode).First(&data).Error
	return &data, err
}

func (a *application) GetByBarcodes(c *gin.Context, barcode []string) ([]*model.RenewApplication, error) {
	var data []*model.RenewApplication
	err := a.db.WithContext(c).Model(&model.RenewApplication{}).Where("barcode in ?", barcode).Scan(&data).Error
	return data, err
}

func (a *application) Completed(c *gin.Context, id []uint, handlerID uint) error {
	err := a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&model.RenewApplication{}).Where("id in (?)", id).Updates(map[string]interface{}{
			"status": consts.RenewStatusCompleted,
		}).Error
		if err != nil {
			return err
		}
		// 批量插入状态
		var status []*model.RenewApplicationStatus
		for _, v := range id {
			status = append(status, &model.RenewApplicationStatus{
				ApplicationID: v,
				HandlerID:     handlerID,
				Status:        consts.RenewStatusCompleted,
				Comment:       "已完成",
			})
		}
		err = tx.Create(&status).Error
		return err
	})
	return err
}
