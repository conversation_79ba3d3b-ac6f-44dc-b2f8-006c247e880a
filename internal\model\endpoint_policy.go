package model

import (
	"marketing/internal/pkg/types"
	"time"
)

// EndpointPolicy represents the endpoint_policy table in your database.
type EndpointPolicy struct {
	ID               int           `gorm:"primaryKey;autoIncrement;column:id"`
	Name             string        `gorm:"size:255;not null;comment:'政策名称';column:name"`
	Description      string        `gorm:"size:255;comment:'政策描述';column:description"`
	File             string        `gorm:"type:text;column:file"`
	StartDate        time.Time     `gorm:"column:start_date"`
	EndDate          time.Time     `gorm:"column:end_date"`
	UpdatedAt        *time.Time    `gorm:"column:updated_at"`
	CreatedAt        time.Time     `gorm:"column:created_at"`
	MaterialSupport  int           `gorm:"type:tinyint;default:0;comment:'是否支持物料，0为否，1为是';column:material_support"`
	AmountSupport    int           `gorm:"default:0;comment:'支持的金额，单位元';column:amount_support"`
	Installments     int           `gorm:"default:0;comment:'分期期数';column:installments"`
	EndpointType     int           `gorm:"not null;default:0;comment:'终端类型，0-未知，1-专柜，2-运营商渠道，3-专卖店，4-城市综合体 5-商超 6-书店';column:endpoint_type"`
	Maximum          int           `gorm:"not null;default:0;comment:'数量上限';column:maximum"`
	WriteOffTable    types.OssPath `gorm:"size:255;null;default:null;comment:'批复表模板';column:write_off_table"`
	Template         string        `gorm:"size:50;null;default:null;comment:'前端模板，跟流程节点和界面显示有关系';column:template"`
	WorkflowTemplate int           `gorm:"default:0;comment:'关联的工作流模板ID，0表示使用默认工作流';column:workflow_template"`
	Enabled          int           `gorm:"not null;default:1;comment:'0禁用 1启用,政策必须在有效期内才有效，也可以提前禁用政策';column:enabled"`
}

// TableName overrides the table name used by GORM.
func (EndpointPolicy) TableName() string {
	return "endpoint_policy"
}

// WorkflowTemplate represents the workflow_templates table
type WorkflowTemplate struct {
	ID          int       `gorm:"primaryKey;column:id;type:int"`
	Name        string    `gorm:"size:255;not null;column:name"`
	Slug        string    `gorm:"size:50;not null;column:slug;uniqueIndex"`
	Description string    `gorm:"type:text;column:description"`
	Config      string    `gorm:"type:json;not null;column:config"`
	Version     string    `gorm:"size:20;default:'1.0';column:version"`
	IsActive    int       `gorm:"default:1;column:is_active"`
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

func (WorkflowTemplate) TableName() string {
	return "workflow_templates"
}

// EndpointApplicationStateLog represents the endpoint_application_state_log table
type EndpointApplicationStateLog struct {
	ID            int64     `gorm:"primaryKey;autoIncrement;column:id"`
	ApplicationID int       `gorm:"not null;column:application_id;index:idx_application_id"`
	FromState     int       `gorm:"not null;column:from_state"`
	ToState       int       `gorm:"not null;column:to_state"`
	Action        string    `gorm:"size:50;not null;column:action"`
	ActorID       int       `gorm:"not null;column:actor_id"`
	ActorType     string    `gorm:"type:enum('admin','agency');not null;column:actor_type"`
	Context       string    `gorm:"type:json;column:context"`
	CreatedAt     time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;index:idx_created_at"`
}

func (EndpointApplicationStateLog) TableName() string {
	return "endpoint_application_state_log"
}
