package processors

import (
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"
	"time"

	"github.com/gin-gonic/gin"
)

// WriteOffProcessor 核销处理器
type WriteOffProcessor struct{}

// NewWriteOffProcessor 创建核销处理器
func NewWriteOffProcessor() *WriteOffProcessor {
	return &WriteOffProcessor{}
}

// Process 处理核销逻辑
func (p *WriteOffProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}
	action := req.Action // 约定: "approve" | "reject"
	handlerMan := ctx.GetString("real_name")

	updateData["write_off_man"] = handlerMan
	updateData["write_off_time"] = time.Now()
	writeOffAdvice, exists := req.Data["write_off_advice"]
	updateData["write_off_advice"] = writeOffAdvice
	if action == "reject" {
		// 审核不通过：校验备注
		if !exists || writeOffAdvice == "" {
			return errors.NewErr("审核不通过，备注不能为空")
		}
		return nil
	}

	return p.handleWriteOff(ctx, txRepo, req, updateData)
}

// handleWriteOff 处理核销初审
func (p *WriteOffProcessor) handleWriteOff(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, req *statemachine.StateTransitionRequest, updateData map[string]any) error {

	if pay, exists := req.Data["pay"]; exists {
		updateData["pay"] = pay
	}

	if bagSupportAmount, exists := req.Data["bag_support_amount"]; exists {
		updateData["bag_support_amount"] = bagSupportAmount
	}

	if writeOffAdvice, exists := req.Data["write_off_advice"]; exists {
		updateData["write_off_advice"] = writeOffAdvice
	}

	// 获取申请信息并更新关联终端的图片
	ea, err := txRepo.GetEndpointApplyByID(ctx, int(req.ApplicationID))
	if err != nil {
		return err
	}
	if ea == nil {
		return nil
	}

	// 如果有关联的终端ID，尝试获取装修图片并更新终端图片
	if ea.AddToEndpointId > 0 {
		// 从回传数据中获取装修图片
		var renovationPhotos string
		if photos, exists := req.Data["renovation_photos"]; exists {
			if photosStr, ok := photos.(string); ok && photosStr != "" {
				renovationPhotos = photosStr
			}
		}

		// 如果有装修图片，更新终端图片
		if renovationPhotos != "" {
			txDB := txRepo.GetDB(ctx)
			if txDB != nil {
				// 更新关联终端的图片字段
				err = txDB.Table("endpoint").
					Where("id = ?", ea.AddToEndpointId).
					Update("images", renovationPhotos).Error
				if err != nil {
					return err
				}
			}
		}
	}

	// 注意：日志记录由外层状态机统一处理，避免重复记录

	return nil
}

// GetName 获取处理器名称
func (p *WriteOffProcessor) GetName() string {
	return "writeoff"
}
