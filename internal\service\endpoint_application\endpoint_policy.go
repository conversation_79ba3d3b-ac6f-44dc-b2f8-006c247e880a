package endpoint_application

import (
	"marketing/internal/api/endpoint_application"
	endpointApplicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
)

// EndpointPolicyService 终端政策服务接口
type EndpointPolicyService interface {
	// CreateEndpointPolicy 创建终端政策
	CreateEndpointPolicy(c *gin.Context, req *endpoint_application.CreateEndpointPolicyReq) error
	// DeleteEndpointPolicy 删除终端政策
	DeleteEndpointPolicy(c *gin.Context, id int) error
	// UpdateEndpointPolicy 更新终端政策
	UpdateEndpointPolicy(c *gin.Context, req *endpoint_application.UpdateEndpointPolicyReq) error
	//UpdateEndpointPolicyEnabled 更新终端政策状态
	UpdateEndpointPolicyEnabled(c *gin.Context, ID, enabled int) error
	// GetEndpointPolicyByID 根据ID获取终端政策
	GetEndpointPolicyByID(c *gin.Context, id int) (*endpoint_application.EndpointPolicyResp, error)
	// GetEndpointPolicyList 获取终端政策列表
	GetEndpointPolicyList(c *gin.Context, req *endpoint_application.EndpointPolicyListReq) ([]*endpoint_application.EndpointPolicyResp, int64, error)
}

// EndpointPolicyServiceImpl 终端政策服务实现
type EndpointPolicyServiceImpl struct {
	dao         endpointApplicationDao.EndpointPolicyDao
	workflowDao endpointApplicationDao.WorkflowConfigDao
}

// NewEndpointPolicyService 创建终端政策服务实例
func NewEndpointPolicyService(dao endpointApplicationDao.EndpointPolicyDao, workflowDao endpointApplicationDao.WorkflowConfigDao) EndpointPolicyService {
	return &EndpointPolicyServiceImpl{
		dao:         dao,
		workflowDao: workflowDao,
	}
}

// CreateEndpointPolicy 创建终端政策
func (s *EndpointPolicyServiceImpl) CreateEndpointPolicy(c *gin.Context, req *endpoint_application.CreateEndpointPolicyReq) error {
	// 时间校验
	startDate, err := time.Parse(time.DateOnly, req.StartDate)
	if err != nil {
		return errors.NewErr("无效的时间格式")
	}
	endDate, err := time.Parse(time.DateOnly, req.EndDate)
	if err != nil {
		return errors.NewErr("无效的时间格式")
	}
	// 验证开始时间和结束时间
	if endDate.Before(startDate) {
		return errors.NewErr("结束时间不能早于开始时间")
	}

	// 验证工作流模板（如果指定了的话）
	if req.WorkflowTemplate > 0 {
		if err := s.validateWorkflowTemplate(c, req.WorkflowTemplate); err != nil {
			return err
		}
	}

	policy := &model.EndpointPolicy{
		Name:             req.Name,
		Description:      req.Description,
		File:             req.File,
		StartDate:        startDate,
		EndDate:          endDate,
		MaterialSupport:  req.MaterialSupport,
		AmountSupport:    req.AmountSupport,
		Installments:     req.Installments,
		EndpointType:     req.EndpointType,
		Maximum:          req.Maximum,
		WriteOffTable:    req.WriteOffTable,
		Template:         req.Template,
		WorkflowTemplate: req.WorkflowTemplate,
		Enabled:          req.Enabled,
	}

	return s.dao.CreateEndpointPolicy(c, policy)
}

// DeleteEndpointPolicy 删除终端政策
func (s *EndpointPolicyServiceImpl) DeleteEndpointPolicy(c *gin.Context, id int) error {
	// 检查政策是否存在
	policy, err := s.dao.GetEndpointPolicyByID(c, id)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("政策不存在")
	}

	return s.dao.DeleteEndpointPolicy(c, id)
}

// UpdateEndpointPolicy 更新终端政策
func (s *EndpointPolicyServiceImpl) UpdateEndpointPolicy(c *gin.Context, req *endpoint_application.UpdateEndpointPolicyReq) error {
	// 检查政策是否存在
	policy, err := s.dao.GetEndpointPolicyByID(c, req.ID)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("政策不存在")
	}
	// 时间校验
	startDate, err := time.Parse(time.DateOnly, req.StartDate)
	if err != nil {
		return errors.NewErr("无效的时间格式")
	}
	endDate, err := time.Parse(time.DateOnly, req.EndDate)
	if err != nil {
		return errors.NewErr("无效的时间格式")
	}

	// 验证时间逻辑
	if !startDate.IsZero() && !endDate.IsZero() && endDate.Before(startDate) {
		return errors.NewErr("结束时间不能早于开始时间")
	}

	// 验证工作流模板（如果指定了的话）
	if req.WorkflowTemplate > 0 {
		if err := s.validateWorkflowTemplate(c, req.WorkflowTemplate); err != nil {
			return err
		}
	}
	// 状态流转

	// 构建更新字段
	updateMap := map[string]interface{}{
		"name":              req.Name,
		"description":       req.Description,
		"file":              req.File,
		"start_date":        req.StartDate,
		"end_date":          req.EndDate,
		"material_support":  req.MaterialSupport,
		"amount_support":    req.AmountSupport,
		"installments":      req.Installments,
		"endpoint_type":     req.EndpointType,
		"maximum":           req.Maximum,
		"write_off_table":   req.WriteOffTable,
		"template":          req.Template,
		"workflow_template": req.WorkflowTemplate,
		"enabled":           req.Enabled,
	}

	// 记录日志：检查哪个字段改变并记录新的值
	go s.LogPolicyChange(c, policy, req)

	// 执行更新操作
	return s.dao.UpdateEndpointPolicy(c, req.ID, updateMap)
}

// UpdateEndpointPolicyEnabled 更新政策状态
func (s *EndpointPolicyServiceImpl) UpdateEndpointPolicyEnabled(c *gin.Context, ID, enabled int) error {
	// 检查政策是否存在
	policy, err := s.dao.GetEndpointPolicyByID(c, ID)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("政策不存在")
	}
	if policy.Enabled == enabled {
		return errors.NewErr("无需修改")
	}
	// 构建更新字段
	updateMap := map[string]interface{}{
		"enabled": enabled,
	}

	// 记录日志：检查哪个字段改变并记录新的值
	oldData := make(map[string]any)
	newData := make(map[string]any)
	oldData["enabled"] = policy.Enabled
	newData["enabled"] = enabled
	go s.dao.PolicyLog(c, policy.ID, oldData, newData)

	// 执行更新操作
	return s.dao.UpdateEndpointPolicy(c, ID, updateMap)
}

// GetEndpointPolicyByID 根据ID获取终端政策
func (s *EndpointPolicyServiceImpl) GetEndpointPolicyByID(c *gin.Context, id int) (*endpoint_application.EndpointPolicyResp, error) {
	policy, err := s.dao.GetEndpointPolicyByID(c, id)
	if err != nil {
		return nil, err
	}
	if policy == nil {
		return nil, errors.NewErr("政策不存在")
	}

	resp := &endpoint_application.EndpointPolicyResp{
		ID:               policy.ID,
		Name:             policy.Name,
		Description:      policy.Description,
		File:             policy.File,
		StartDate:        policy.StartDate.Format("2006-01-02"),
		EndDate:          policy.EndDate.Format("2006-01-02"),
		CreatedAt:        policy.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        types.CustomTime(*policy.UpdatedAt),
		MaterialSupport:  policy.MaterialSupport,
		AmountSupport:    policy.AmountSupport,
		Installments:     policy.Installments,
		EndpointType:     policy.EndpointType,
		Maximum:          policy.Maximum,
		Template:         policy.Template,
		WorkflowTemplate: policy.WorkflowTemplate,
		Enabled:          policy.Enabled,
	}

	return resp, nil
}

// GetEndpointPolicyList 获取终端政策列表
func (s *EndpointPolicyServiceImpl) GetEndpointPolicyList(c *gin.Context, req *endpoint_application.EndpointPolicyListReq) ([]*endpoint_application.EndpointPolicyResp, int64, error) {

	policies, total, err := s.dao.GetEndpointPolicyList(c, req.Name, req.Enabled, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	respList := make([]*endpoint_application.EndpointPolicyResp, len(policies))
	for i, policy := range policies {
		respList[i] = &endpoint_application.EndpointPolicyResp{
			ID:               policy.ID,
			Name:             policy.Name,
			Description:      policy.Description,
			File:             policy.File,
			StartDate:        policy.StartDate.Format("2006-01-02"),
			EndDate:          policy.EndDate.Format("2006-01-02"),
			CreatedAt:        policy.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:        types.CustomTime(*policy.UpdatedAt),
			MaterialSupport:  policy.MaterialSupport,
			AmountSupport:    policy.AmountSupport,
			Installments:     policy.Installments,
			EndpointType:     policy.EndpointType,
			Maximum:          policy.Maximum,
			Template:         policy.Template,
			WorkflowTemplate: policy.WorkflowTemplate,
			Enabled:          policy.Enabled,
		}
	}

	return respList, total, nil
}

func (s *EndpointPolicyServiceImpl) LogPolicyChange(c *gin.Context, old *model.EndpointPolicy, update *endpoint_application.UpdateEndpointPolicyReq) {
	oldData := make(map[string]any)
	newData := make(map[string]any)
	oldFormat := endpoint_application.UpdateEndpointPolicyReq{
		ID:               old.ID,
		Name:             old.Name,
		Description:      old.Description,
		File:             old.File,
		StartDate:        old.StartDate.Format(time.DateOnly),
		EndDate:          old.EndDate.Format(time.DateOnly),
		MaterialSupport:  old.MaterialSupport,
		AmountSupport:    old.AmountSupport,
		Installments:     old.Installments,
		EndpointType:     old.EndpointType,
		Maximum:          old.Maximum,
		Template:         old.Template,
		WorkflowTemplate: old.WorkflowTemplate,
		Enabled:          old.Enabled,
	}

	utils.CompareFieldChanges(oldFormat, update, &oldData, &newData)

	if len(oldData) > 0 || len(newData) > 0 {
		s.dao.PolicyLog(c, old.ID, oldData, newData)
	}
}

// validateWorkflowTemplate 验证工作流模板是否存在
func (s *EndpointPolicyServiceImpl) validateWorkflowTemplate(c *gin.Context, templateID int) error {
	templates, err := s.workflowDao.GetWorkflowTemplates(c)
	if err != nil {
		return errors.NewErr("获取工作流模板失败")
	}

	for _, template := range templates {
		if template.ID == templateID {
			return nil // 找到了，验证通过
		}
	}

	return errors.NewErr("指定的工作流模板不存在")
}
