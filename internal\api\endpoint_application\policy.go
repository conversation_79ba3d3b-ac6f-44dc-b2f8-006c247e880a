package endpoint_application

import (
	"marketing/internal/api"
	"marketing/internal/pkg/statemachine"
	"marketing/internal/pkg/types"
)

// CreateEndpointPolicyReq 创建终端政策请求
type CreateEndpointPolicyReq struct {
	Name             string        `json:"name" form:"name" binding:"required"`
	Description      string        `json:"description" form:"description"`
	File             string        `json:"file" form:"file"`
	StartDate        string        `json:"start_date" form:"start_date" binding:"required"`
	EndDate          string        `json:"end_date" form:"end_date" binding:"required"`
	MaterialSupport  int           `json:"material_support" form:"material_support"`
	AmountSupport    int           `json:"amount_support" form:"amount_support"`
	Installments     int           `json:"installments" form:"installments"`
	EndpointType     int           `json:"endpoint_type" form:"endpoint_type"`
	Maximum          int           `json:"maximum" form:"maximum"`
	WriteOffTable    types.OssPath `json:"write_off_table" form:"write_off_table"`
	Template         string        `json:"template" form:"template"`
	WorkflowTemplate int           `json:"workflow_template" form:"workflow_template"`
	Enabled          int           `json:"enabled" form:"enabled"`
}

// UpdateEndpointPolicyReq 更新终端政策请求
type UpdateEndpointPolicyReq struct {
	ID               int           `json:"id" form:"id"`
	Name             string        `json:"name" form:"name"`
	Description      string        `json:"description" form:"description"`
	File             string        `json:"file" form:"file"`
	StartDate        string        `json:"start_date" form:"start_date"`
	EndDate          string        `json:"end_date" form:"end_date"`
	MaterialSupport  int           `json:"material_support" form:"material_support"`
	AmountSupport    int           `json:"amount_support" form:"amount_support"`
	Installments     int           `json:"installments" form:"installments"`
	EndpointType     int           `json:"endpoint_type" form:"endpoint_type"`
	Maximum          int           `json:"maximum" form:"maximum"`
	WriteOffTable    types.OssPath `json:"write_off_table" form:"write_off_table"`
	Template         string        `json:"template" form:"template"`
	WorkflowTemplate int           `json:"workflow_template" form:"workflow_template"`
	Enabled          int           `json:"enabled" form:"enabled"`
}

// EndpointPolicyListReq 终端政策列表请求
type EndpointPolicyListReq struct {
	api.PaginationParams
	Name    string `json:"name" form:"name"` // 政策名称搜索
	Enabled *int   `json:"enabled" form:"enabled"`
}

// EndpointPolicyResp 终端政策响应
type EndpointPolicyResp struct {
	ID               int              `json:"id"`
	Name             string           `json:"name"`
	Description      string           `json:"description"`
	File             string           `json:"file"`
	StartDate        string           `json:"start_date"`
	EndDate          string           `json:"end_date"`
	CreatedAt        string           `json:"created_at"`
	UpdatedAt        types.CustomTime `json:"updated_at"`
	MaterialSupport  int              `json:"material_support" form:"material_support"`
	AmountSupport    int              `json:"amount_support" form:"amount_support"`
	Installments     int              `json:"installments" form:"installments"`
	EndpointType     int              `json:"endpoint_type" form:"endpoint_type"`
	Maximum          int              `json:"maximum" form:"maximum"`
	Template         string           `json:"template" form:"template"`
	WorkflowTemplate int              `json:"workflow_template" form:"workflow_template"`
	Enabled          int              `json:"enabled" form:"enabled"`
}

// StateTransitionReq 通用状态转换请求
type StateTransitionReq struct {
	ID     uint                   `json:"id" binding:"required"`
	Action string                 `json:"action" binding:"required"`
	Data   map[string]interface{} `json:"data"`
}

// GetAvailableActionsResp 获取可用操作响应
type GetAvailableActionsResp struct {
	Actions      []ActionInfo `json:"actions"`
	CurrentState int          `json:"current_state"`
	PolicyType   string       `json:"policy_type"`
}

// ActionInfo 操作信息
type ActionInfo struct {
	Type   string                 `json:"type"`
	Label  string                 `json:"label"`
	Config map[string]interface{} `json:"config"`
}

// WorkflowTemplateResp 工作流模板响应
type WorkflowTemplateResp struct {
	ID          int              `json:"id"`
	Name        string           `json:"name"`
	Slug        string           `json:"slug"`
	Description string           `json:"-"`
	Version     string           `json:"-"`
	IsActive    int              `json:"is_active"`
	CreatedAt   types.CustomTime `json:"created_at"`
	UpdatedAt   types.CustomTime `json:"updated_at"`
}

// CreateWorkflowTemplateReq 创建工作流模板请求
type CreateWorkflowTemplateReq struct {
	Name        string                       `json:"name" binding:"required"`
	Slug        string                       `json:"slug" binding:"required"`
	Description string                       `json:"description"`
	Version     string                       `json:"-"`
	Config      *statemachine.WorkflowConfig `json:"config" binding:"required"`
}

// UpdateWorkflowTemplateReq 更新工作流模板请求
type UpdateWorkflowTemplateReq struct {
	Name        string                       `json:"name"`
	Description string                       `json:"description"`
	Version     string                       `json:"-"`
	Config      *statemachine.WorkflowConfig `json:"config"`
}

type WorkflowTemplateDetailResp struct {
	ID          int                          `json:"id"`
	Name        string                       `json:"name"`
	Slug        string                       `json:"slug"`
	Description string                       `json:"description"`
	Version     string                       `json:"version"`
	Config      *statemachine.WorkflowConfig `json:"config"`
	IsActive    int                          `json:"is_active"`
	CreatedAt   types.CustomTime             `json:"created_at"`
	UpdatedAt   types.CustomTime             `json:"updated_at"`
}
