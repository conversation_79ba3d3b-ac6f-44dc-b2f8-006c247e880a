# 建店申请工作流API文档

## 概述

建店申请和建店回传接口已升级为使用动态工作流模式，支持根据不同政策使用不同的工作流模板，提供更灵活和标准化的状态管理。

## 🎯 核心特性

### 1. 动态工作流选择
- **政策驱动**: 每个政策可以指定不同的工作流模板
- **自动适配**: 系统根据申请的政策ID自动选择对应的工作流
- **向下兼容**: 未指定工作流模板的政策自动使用标准工作流

### 2. 工作流模板管理
- **模板化配置**: 工作流配置以JSON格式存储在数据库中
- **版本控制**: 支持工作流模板的版本管理
- **热更新**: 修改工作流模板后立即生效，无需重启服务

### 3. 灵活的状态机
- **多工作流支持**: 同时支持多种不同的工作流
- **状态验证**: 每个工作流都有独立的状态转换规则
- **操作权限**: 根据工作流配置控制可用操作

## API变更说明

### 1. 建店申请创建 (已修改)

**接口**: `POST /agency/endpoint-applications`

**变更内容**:
- 创建申请时自动使用工作流的初始状态
- 自动计算下一状态提示

**响应示例**:
```json
{
  "code": 200,
  "message": "success"
}
```

### 2. 建店申请列表 (已增强)

**接口**: `GET /agency/endpoint-applications`

**新增响应字段**:
```json
{
  "code": 200,
  "data": [
    {
      "id": 123,
      "name": "测试店铺",
      "state": 0,
      "state_name": "待审核",
      "next_state": 100,
      "next_state_name": "审核通过",
      // 新增工作流字段
      "available_actions": [
        {
          "type": "approve",
          "label": "审核通过",
          "config": {
            "create_endpoint": true
          }
        },
        {
          "type": "reject",
          "label": "审核拒绝",
          "config": {
            "require_remark": true
          }
        }
      ],
      "current_state_info": {
        "id": 0,
        "name": "待审核",
        "type": "audit"
      }
    }
  ]
}
```

### 3. 建店申请详情 (新增)

**接口**: `GET /agency/endpoint-applications/{id}`

**描述**: 获取申请的详细信息，包含完整的工作流状态和可用操作

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "id": 123,
    "name": "测试店铺",
    "state": 0,
    "state_name": "待审核",
    "available_actions": [
      {
        "type": "approve",
        "label": "审核通过",
        "config": {
          "create_endpoint": true
        }
      }
    ],
    "current_state_info": {
      "id": 0,
      "name": "待审核",
      "type": "audit"
    },
    "workflow_info": {
      "name": "常规建店工作流",
      "slug": "standard",
      "current_state": 0,
      "start_state": 0
    },
    "postback_data": {
      "write_off_table": "批复表文件路径",
      "lease_contract": "租赁合同文件路径",
      "annual_rent": 50000.00,
      "renovation_photos": "装修照片JSON数据"
    }
  }
}
```

### 4. 获取可用操作 (新增)

**接口**: `GET /agency/endpoint-applications/{id}/actions`

**描述**: 获取当前状态下可执行的操作列表

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "actions": [
      {
        "type": "approve",
        "label": "审核通过",
        "config": {
          "create_endpoint": true
        }
      },
      {
        "type": "reject",
        "label": "审核拒绝",
        "config": {
          "require_remark": true
        }
      }
    ],
    "current_state": 0,
    "policy_type": "standard"
  }
}
```

### 5. 状态转换 (新增)

**接口**: `POST /agency/endpoint-applications/{id}/transition`

**描述**: 执行状态转换操作

**请求参数**:
```json
{
  "action": "approve",
  "data": {
    "audit_user_id": 456,
    "remark": "审核通过，材料齐全"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "状态转换成功"
}
```

### 6. 建店回传 (已修改)

**接口**: `POST /agency/endpoint-applications/{id}/postback`

**变更内容**:
- 现在使用工作流模式进行状态转换
- 内部调用状态转换接口，action为"complete_store"

**请求参数** (保持不变):
```json
{
  "write_off_table": "批复表文件路径",
  "lease_contract": "租赁合同文件路径",
  "annual_rent": 50000.00,
  "design_renderings": ["设计效果图"],
  "renovation_photos": ["装修照片"],
  "renovation_videos": ["装修视频"],
  "diploma": ["资质证书"],
  "endpoint_group_photo": ["终端合影"],
  "extend": {},
  "remark": "回传说明"
}
```

## 工作流状态说明

| 状态ID | 状态名称 | 状态类型 | 可用操作 | 下一状态 |
|--------|----------|----------|----------|----------|
| 0 | 待审核 | audit | approve, reject | 100, -100 |
| 100 | 审核通过 | material | submit_material | 200 |
| 200 | 物料支持完成 | material_supported | complete_store | 300 |
| 300 | 核销已回传，待初审 | write_off | approve, reject | 400, -400 |
| 400 | 初审通过,待渠道审核 | channel_audit | approve, reject | 500, -500 |
| 500 | 渠道审核通过 | completed | - | - |
| -100 | 审核不通过 | rejected | - | - |
| -400 | 核销初审不通过 | rejected | - | - |
| -500 | 渠道审核不通过 | rejected | - | - |
| -900 | 建店终止 | canceled | - | - |

## 操作类型说明

| 操作类型 | 操作名称 | 适用状态 | 必需参数 | 说明 |
|----------|----------|----------|----------|------|
| approve | 审核通过 | 0, 300, 400 | - | 审核通过操作 |
| reject | 审核拒绝 | 0, 300, 400 | remark | 审核拒绝，需要备注 |
| submit_material | 申请物料支持 | 100 | material_list | 提交物料申请 |
| complete_store | 回传建店资料 | 200 | 回传数据 | 完成建店并回传资料 |

## 前端集成建议

### 1. 列表页面
- 根据 `available_actions` 显示可用的操作按钮
- 根据 `current_state_info` 显示当前状态信息
- 隐藏不可用的操作

### 2. 详情页面
- 调用详情接口获取完整信息
- 显示工作流进度条
- 根据可用操作显示相应的表单

### 3. 操作执行
- 使用统一的状态转换接口
- 根据操作类型显示不同的表单字段
- 操作成功后刷新页面状态

### 4. 错误处理
- 处理状态转换失败的情况
- 显示具体的错误信息
- 提供重试机制

## 工作流模板配置

### 数据库表结构

#### workflow_template 表
```sql
CREATE TABLE workflow_template (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '工作流名称',
    slug VARCHAR(50) NOT NULL COMMENT '工作流标识',
    config TEXT NOT NULL COMMENT '工作流配置JSON',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### endpoint_policy 表增加字段
```sql
ALTER TABLE endpoint_policy
ADD COLUMN workflow_template INT DEFAULT 0 COMMENT '工作流模板ID，0表示使用标准工作流';
```

### 工作流配置示例

#### 标准工作流 (slug: "standard")
```json
{
  "name": "标准建店工作流",
  "slug": "standard",
  "start_state": 0,
  "validators": ["permission", "data"],
  "processors": ["audit", "postback"],
  "states": {
    "0": {
      "id": 0,
      "name": "待审核",
      "type": "audit",
      "actions": [
        {
          "type": "approve",
          "label": "审核通过",
          "config": {"next_state": 100}
        },
        {
          "type": "reject",
          "label": "审核拒绝",
          "config": {"next_state": -100, "require_remark": true}
        }
      ]
    },
    "100": {
      "id": 100,
      "name": "审核通过",
      "type": "material",
      "actions": [
        {
          "type": "complete_store",
          "label": "回传建店资料",
          "config": {"next_state": 500}
        }
      ]
    }
  },
  "global_rules": []
}
```

#### 快速建店工作流 (slug: "fast")
```json
{
  "name": "快速建店工作流",
  "slug": "fast",
  "start_state": 0,
  "validators": ["permission"],
  "processors": ["audit", "postback"],
  "states": {
    "0": {
      "id": 0,
      "name": "待审核",
      "type": "audit",
      "actions": [
        {
          "type": "approve",
          "label": "审核通过",
          "config": {"next_state": 100}
        },
        {
          "type": "reject",
          "label": "审核拒绝",
          "config": {"next_state": -100}
        }
      ]
    },
    "100": {
      "id": 100,
      "name": "审核通过，直接建店",
      "type": "approved",
      "actions": [
        {
          "type": "complete_store",
          "label": "回传建店资料",
          "config": {"next_state": 500}
        }
      ]
    }
  },
  "global_rules": []
}
```

## 验证器和处理器

### 验证器 (Validators)

验证器负责在状态转换前验证请求的合法性。系统提供以下内置验证器：

#### 1. permission (权限验证器)
- **功能**: 验证用户是否有权限执行特定操作
- **验证内容**:
  - 用户登录状态
  - 操作权限（管理员vs代理商）
  - 代理商只能操作自己的申请
- **适用场景**: 所有工作流都应该包含

#### 2. data (数据验证器)
- **功能**: 验证请求数据的完整性和有效性
- **验证内容**:
  - 拒绝操作必须有备注
  - 建店回传必须有必要文件
  - 数据格式和范围验证
- **适用场景**: 需要严格数据验证的工作流

### 处理器 (Processors)

处理器负责在状态转换时执行具体的业务逻辑。系统提供以下内置处理器：

#### 1. audit (审核处理器)
- **功能**: 处理审核相关的业务逻辑
- **处理内容**:
  - 设置审核时间和状态
  - 记录审核人信息
  - 处理审核备注
- **支持操作**: approve, reject

#### 2. postback (回传处理器)
- **功能**: 处理建店回传数据
- **处理内容**:
  - 保存回传文件路径
  - 处理图片和视频数据
  - 设置回传时间和状态
- **支持操作**: complete_store

### 自定义验证器和处理器

#### 创建自定义验证器
```go
type CustomValidator struct{}

func (v *CustomValidator) Validate(ctx *gin.Context, req *statemachine.StateTransitionRequest) error {
    // 自定义验证逻辑
    return nil
}

func (v *CustomValidator) GetName() string {
    return "custom"
}

// 注册验证器
statemachine.RegisterValidator("custom", &CustomValidator{})
```

#### 创建自定义处理器
```go
type CustomProcessor struct{}

func (p *CustomProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
    // 自定义处理逻辑
    return nil
}

func (p *CustomProcessor) GetName() string {
    return "custom"
}

// 注册处理器
statemachine.RegisterProcessor("custom", &CustomProcessor{})
```

## 使用示例

### 1. 为政策配置工作流模板

```sql
-- 为政策ID=1配置使用快速建店工作流
UPDATE endpoint_policy
SET workflow_template = 2
WHERE id = 1;

-- 为政策ID=2使用标准工作流（默认值0）
UPDATE endpoint_policy
SET workflow_template = 0
WHERE id = 2;
```

### 2. 创建新的工作流模板

```sql
INSERT INTO workflow_template (name, slug, config, is_active)
VALUES (
    '快速建店工作流',
    'fast',
    '{"name":"快速建店工作流","slug":"fast","start_state":0,...}',
    1
);
```

## 注意事项

1. **权限控制**: 所有接口都会验证用户权限，确保只能操作自己代理商的申请
2. **状态验证**: 状态转换会通过工作流引擎验证，非法转换会被拒绝
3. **数据完整性**: 回传数据会在事务中处理，确保数据一致性
4. **向后兼容**: 现有的回传接口保持兼容，但内部使用工作流模式
5. **扩展性**: 工作流模式支持未来添加新的状态和操作
6. **动态加载**: 工作流模板修改后立即生效，无需重启服务
7. **政策隔离**: 不同政策可以使用完全不同的工作流，互不影响
8. **默认行为**: 未配置工作流模板的政策自动使用标准工作流
