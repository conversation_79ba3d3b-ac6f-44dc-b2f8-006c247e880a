# 物料数据获取示例

本文档展示了 `GetEndpointApplyDetail` 方法中物料数据获取的实现。

## 功能概述

物料数据获取支持新旧版本的兼容性：

### 新版本（推荐）
- 数据存储在 `endpoint_material_support` 表中
- 结构化存储，查询效率高
- 包含完整的物料信息

### 旧版本（兼容）
- 数据存储在 `endpoint_application.extend` 字段中
- 支持两种格式：
  1. `material_support_detail` - JSON 字符串格式
  2. `material_support_detail_map` + `material_support_detail_amount` - Map 格式

## 数据获取优先级

1. **优先级1**：从 `endpoint_material_support` 表获取
2. **优先级2**：从 `extend` 字段的 `material_support_detail_map` 获取（解析成结构化格式）
3. **优先级3**：从 `extend` 字段的 `material_support_detail` 获取（返回原始字符串）

## 数据格式示例

### 新版本表结构

```sql
CREATE TABLE endpoint_material_support (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    num INT NOT NULL,
    pic VARCHAR(255),
    price DECIMAL(10,2) NOT NULL,
    production_number VARCHAR(50),
    thumbnail VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 旧版本格式1 - 原始字符串

```json
{
    "material_support_detail": "投影仪2台，音响1台，总价3800元"
}
```

### 旧版本格式2 - Map 格式

```json
{
    "material_support_detail_map": {
        "投影仪": {
            "quantity": 2,
            "price": 1500.00
        },
        "音响": {
            "quantity": 1,
            "price": 800.00
        }
    },
    "material_support_detail_amount": 3800.00
}
```

## API 响应格式

### 结构化格式（新版本和 Map 格式）

```json
{
    "material_data": {
        "material_list": [
            {
                "id": 1,
                "name": "投影仪",
                "quantity": 2,
                "price": 1500.00,
                "amount": 3000.00
            },
            {
                "id": 2,
                "name": "音响",
                "quantity": 1,
                "price": 800.00,
                "amount": 800.00
            }
        ],
        "total_amount": 3800.00,
        "is_raw_format": false
    }
}
```

### 原始字符串格式

```json
{
    "material_data": {
        "material_list": [],
        "total_amount": 0,
        "raw_detail": "投影仪2台，音响1台，总价3800元",
        "is_raw_format": true
    }
}
```

## 错误处理

- 如果获取新版数据失败，会记录错误日志但不影响整体响应
- 如果旧版数据格式不正确，会记录警告日志并跳过
- 所有解析错误都会被捕获并记录，确保接口稳定性

## 使用方式

在 `GetEndpointApplyDetail` 方法中自动调用，无需额外配置：

```go
// 获取物料数据
materialData, err := e.getMaterialData(c, apply)
if err != nil {
    log.Error("获取物料数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
} else {
    detail.MaterialData = materialData
}
```

## 迁移建议

对于使用旧版格式的数据，建议：

1. 逐步将数据迁移到新表 `endpoint_material_support`
2. 新的物料申请直接使用新表存储
3. 保持旧版本兼容性，直到所有历史数据迁移完成
