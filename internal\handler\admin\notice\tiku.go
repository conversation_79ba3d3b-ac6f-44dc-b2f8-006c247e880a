package notice

import (
	"marketing/internal/api/notice"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"

	"github.com/gin-gonic/gin"
)

type TiKuHandler interface {
	// UploadPaperAndHomework 上传试卷和作业本Excel文件
	UploadPaperAndHomework(c *gin.Context)
	// GetTextBookOptions 获取教材类型选项
	GetTextBookOptions(c *gin.Context)
	// GetTextBookHistories 获取教材历史记录
	GetTextBookHistories(c *gin.Context)
	// GetAppOptions 获取App选项
	GetAppOptions(c *gin.Context)
	// GetAppUpdateHistories 获取App更新历史
	GetAppUpdateHistories(c *gin.Context)
}

type tiKuHandler struct {
	svc    service.TiKuSvc
	appSvc service.AppUpdateSvc
}

func NewTiKuHandler(svc service.TiKuSvc, appSvc service.AppUpdateSvc) TiKuHandler {
	return &tiKuHandler{
		svc:    svc,
		appSvc: appSvc,
	}
}

// UploadPaperAndHomework 上传试卷和作业本Excel文件
func (h *tiKuHandler) UploadPaperAndHomework(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		handler.Error(c, errors.NewErr("获取上传文件失败: "+err.Error()))
		return
	}

	_, err = h.svc.UploadPaperAndHomework(c, file)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// GetTextBookOptions 获取教材类型选项
func (h *tiKuHandler) GetTextBookOptions(c *gin.Context) {
	options, err := h.svc.GetTextBookOptions(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, options)
}

// GetTextBookHistories 获取教材历史记录
func (h *tiKuHandler) GetTextBookHistories(c *gin.Context) {
	var req notice.TextBookHistoriesReq
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	histories, err := h.svc.GetTextBookHistories(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, histories)
}

// GetAppOptions 获取App选项
func (h *tiKuHandler) GetAppOptions(c *gin.Context) {
	options, err := h.appSvc.GetAppOptions(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, options)
}

// GetAppUpdateHistories 获取App更新历史
func (h *tiKuHandler) GetAppUpdateHistories(c *gin.Context) {
	var req notice.AppUpdateHistoriesReq
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	histories, err := h.appSvc.GetAppUpdateHistories(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, histories)
}
