package endpoint_application

import (
	"encoding/json"
	"marketing/internal/model"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// WorkflowConfigDao 工作流配置数据访问接口
type WorkflowConfigDao interface {

	// GetWorkflowTemplates 获取工作流模板列表
	GetWorkflowTemplates(ctx *gin.Context) ([]*model.WorkflowTemplate, error)

	// CreateWorkflowTemplate 创建工作流模板
	CreateWorkflowTemplate(ctx *gin.Context, template *model.WorkflowTemplate) error

	// UpdateWorkflowTemplate 更新工作流模板
	UpdateWorkflowTemplate(ctx *gin.Context, id int, template *model.WorkflowTemplate) error

	// DeleteWorkflowTemplate 删除工作流模板
	DeleteWorkflowTemplate(ctx *gin.Context, id int) error

	// GetPolicyByID 根据ID获取政策信息
	GetPolicyByID(policyID int) (statemachine.PolicyModel, error)

	// GetWorkflowTemplateByID 根据ID获取工作流模板
	GetWorkflowTemplateByID(templateID int) (statemachine.WorkflowTemplateModel, error)

	// GetStandardWorkflowConfig 获取标准工作流配置
	GetStandardWorkflowConfig(ctx interface{}) (*statemachine.WorkflowConfigModel, error)
}

// workflowConfigDao 工作流配置数据访问实现
type workflowConfigDao struct {
	db *gorm.DB
}

// NewWorkflowConfigDao 创建工作流配置数据访问实例
func NewWorkflowConfigDao(db *gorm.DB) WorkflowConfigDao {
	return &workflowConfigDao{db: db}
}

// GetWorkflowTemplates 获取工作流模板列表
func (d *workflowConfigDao) GetWorkflowTemplates(ctx *gin.Context) ([]*model.WorkflowTemplate, error) {
	var templates []*model.WorkflowTemplate
	err := d.db.WithContext(ctx).
		Where("is_active = ?", true).
		Find(&templates).Error

	return templates, err
}

// CreateWorkflowTemplate 创建工作流模板
func (d *workflowConfigDao) CreateWorkflowTemplate(ctx *gin.Context, template *model.WorkflowTemplate) error {
	return d.db.WithContext(ctx).Create(template).Error
}

// UpdateWorkflowTemplate 更新工作流模板
func (d *workflowConfigDao) UpdateWorkflowTemplate(ctx *gin.Context, id int, template *model.WorkflowTemplate) error {
	return d.db.WithContext(ctx).
		Where("id = ?", id).
		Updates(template).Error
}

// DeleteWorkflowTemplate 删除工作流模板
func (d *workflowConfigDao) DeleteWorkflowTemplate(ctx *gin.Context, id int) error {
	return d.db.WithContext(ctx).
		Where("id = ?", id).
		Update("is_active", false).Error
}

// GetPolicyByID 根据ID获取政策信息
func (d *workflowConfigDao) GetPolicyByID(policyID int) (statemachine.PolicyModel, error) {
	var policy model.EndpointPolicy
	err := d.db.Where("id = ?", policyID).First(&policy).Error
	if err != nil {
		return nil, err
	}
	return &PolicyModelAdapter{&policy}, nil
}

// GetWorkflowTemplateByID 根据ID获取工作流模板
func (d *workflowConfigDao) GetWorkflowTemplateByID(templateID int) (statemachine.WorkflowTemplateModel, error) {
	var template model.WorkflowTemplate
	err := d.db.Where("id = ? AND is_active = ?", templateID, true).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &WorkflowTemplateModelAdapter{&template}, nil
}

// GetStandardWorkflowConfig 获取标准工作流配置
func (d *workflowConfigDao) GetStandardWorkflowConfig(ctx interface{}) (*statemachine.WorkflowConfigModel, error) {
	// 查找标准工作流模板
	var template model.WorkflowTemplate
	err := d.db.Where("slug = ? AND is_active = ?", "standard", true).First(&template).Error
	if err != nil {
		return nil, err
	}
	adapter := &WorkflowConfigModelAdapter{&template}
	var result statemachine.WorkflowConfigModel = adapter
	return &result, nil
}

// ParseWorkflowConfig 解析工作流配置JSON
func ParseWorkflowConfig(configJSON string) (*statemachine.WorkflowConfig, error) {
	var config statemachine.WorkflowConfig
	err := json.Unmarshal([]byte(configJSON), &config)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// SerializeWorkflowConfig 序列化工作流配置为JSON
func SerializeWorkflowConfig(config *statemachine.WorkflowConfig) (string, error) {
	data, err := json.Marshal(config)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// PolicyModelAdapter 政策模型适配器
type PolicyModelAdapter struct {
	policy *model.EndpointPolicy
}

func (p *PolicyModelAdapter) GetID() int {
	return p.policy.ID
}

func (p *PolicyModelAdapter) GetWorkflowTemplate() int {
	return p.policy.WorkflowTemplate
}

// WorkflowTemplateModelAdapter 工作流模板模型适配器
type WorkflowTemplateModelAdapter struct {
	template *model.WorkflowTemplate
}

func (w *WorkflowTemplateModelAdapter) GetID() int {
	return w.template.ID
}

func (w *WorkflowTemplateModelAdapter) GetName() string {
	return w.template.Name
}

func (w *WorkflowTemplateModelAdapter) GetSlug() string {
	return w.template.Slug
}

func (w *WorkflowTemplateModelAdapter) GetConfig() string {
	return w.template.Config
}

// WorkflowConfigModelAdapter 工作流配置模型适配器
type WorkflowConfigModelAdapter struct {
	template *model.WorkflowTemplate
}

func (w *WorkflowConfigModelAdapter) GetWorkflowConfig() string {
	return w.template.Config
}

func (w *WorkflowConfigModelAdapter) GetVersion() string {
	return "1.0" // 可以从模板中获取版本信息
}

func (w *WorkflowConfigModelAdapter) IsActive() bool {
	return w.template.IsActive == 1
}
