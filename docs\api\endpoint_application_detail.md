# 终端申请详情接口文档

## 终端申请详情
获取单个终端申请的详细信息，包括基本信息、物料数据、回传数据、审核历史和工作流信息。

### 请求信息
- **请求URL**: `/admin/endpoint-application/:id`
- **请求方式**: `GET`

### 路径参数
| 参数名 | 必选 | 类型 | 说明 |
|:------|:-----|:-----|:-----|
| id    | 是   | int  | 终端申请ID |

### 响应数据结构

#### 成功响应示例
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "id": 918,
    "top_agency": 150,
    "top_agency_name": "深圳读书郎教育科技有限公司",
    "second_agency": 0,
    "second_agency_name": "直营",
    "name": "读书郎专卖店(测试店)",
    "type": 3,
    "province": 110000,
    "city": 110100,
    "district": 110102,
    "address": "北京市西城区市政府大楼1层",
    "lng": "116.36600041099146",
    "lat": "39.91220117947085",
    "channel_level": 1,
    "blng": "116.372514",
    "blat": "39.918125",
    "policy_id": 14,
    "phone": "13560319968",
    "manager": "张三",
    "investor": "李四",
    "investor_phone": "13560319968",
    "expect_open_time": "2025-08-12",
    "position": "商业中心",
    "endpoint_area": "120平方米",
    "pay": "50000",
    "add_to_endpoint_id": 1001,
    "state": 2,
    "next_state": 3,
    "application_year": 2025,
    "created_at": "2025-01-15T10:30:00Z",
    "updated_at": "2025-01-15T15:20:00Z",
    "pics": [
      "rbcare/endpoint_application/pics/dbtfwuor03xfepfmho.jpg",
      "rbcare/endpoint_application/pics/dbtfwuotddxm8ljjd4.png"
    ],
    "extend": {
      "design_advise": "建议采用现代简约风格",
      "design_submit_time": "2025-01-15 14:30:00",
      "pics_plane_design": [
        "rbcare/endpoint_application/pics/dbtfwxpa7kzwoefycn.png",
        "rbcare/endpoint_application/pics/dbtfwxpbq5xpgl0pyp.png"
      ]
    },
    "code": 2025001,
    "endpoint_id": 1001,
    "endpoint_code": "BJ001",
    "created_at_formatted": "2025-01-15 10:30:00",
    "endpoint_type_name": "专卖店",
    "state_name": "审核通过",
    "available_actions": [
      {
        "type": "approve",
        "label": "审核通过",
        "config": {
          "required_fields": ["remark"],
          "next_state": 3
        }
      },
      {
        "type": "reject",
        "label": "审核拒绝",
        "config": {
          "required_fields": ["remark"],
          "next_state": 4
        }
      }
    ],
    "current_state_info": {
      "id": 2,
      "name": "待审核",
      "type": "pending"
    },
    "workflow_info": {
      "name": "标准申请流程",
      "slug": "standard",
      "current_state": 2,
      "start_state": 1
    },
    "material_data": {
      "material_list": [
        {
          "id": 1,
          "name": "投影仪",
          "quantity": 2,
          "price": 1500.00,
          "amount": 3000.00
        },
        {
          "id": 2,
          "name": "音响设备",
          "quantity": 1,
          "price": 800.00,
          "amount": 800.00
        }
      ],
      "total_amount": 3800.00,
      "is_raw_format": false
    },
    "postback_data": {
      "write_off_table": "rbcare/documents/writeoff_table_001.pdf",
      "lease_contract": "rbcare/documents/lease_contract_001.pdf",
      "annual_rent": 120000.00,
      "design_renderings": "rbcare/images/design_001.jpg",
      "renovation_photos": "rbcare/images/renovation_001.jpg",
      "renovation_videos": "rbcare/videos/renovation_001.mp4",
      "diploma": "rbcare/documents/diploma_001.pdf",
      "endpoint_group_photo": "rbcare/images/group_photo_001.jpg",
      "extend": "{\"additional_info\": \"额外信息\"}"
    },
    "audit_history": [
      {
        "id": 1,
        "action": "submit",
        "action_name": "提交申请",
        "from_state": 0,
        "to_state": 1,
        "remark": "初次提交",
        "auditor_id": 100,
        "auditor_name": "系统",
        "created_at": "2025-01-15 10:30:00"
      },
      {
        "id": 2,
        "action": "approve",
        "action_name": "审核通过",
        "from_state": 1,
        "to_state": 2,
        "remark": "申请材料齐全，符合开店条件",
        "auditor_id": 101,
        "auditor_name": "审核员张三",
        "created_at": "2025-01-15 15:20:00"
      }
    ]
  }
}
```

#### 原始字符串格式物料数据示例
```json
{
  "material_data": {
    "material_list": [],
    "total_amount": 0,
    "raw_detail": "投影仪2台，音响设备1台，展示架3个，总价值15000元",
    "is_raw_format": true
  }
}
```

### 响应字段说明

#### 基础信息字段
| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| id | int | 申请ID |
| top_agency | int | 一级代理商ID |
| top_agency_name | string | 一级代理商名称 |
| second_agency | int | 二级代理商ID |
| second_agency_name | string | 二级代理商名称 |
| name | string | 终端名称 |
| type | int | 终端类型(1:专柜,2:运营商渠道,3:专卖店,4:城市综合体,5:商超,6:书店) |
| province | int | 省份代码 |
| city | int | 城市代码 |
| district | int | 区域代码 |
| address | string | 详细地址 |
| lng | string | 经度(高德坐标) |
| lat | string | 纬度(高德坐标) |
| channel_level | int | 渠道等级(0:未知,1:省会级,2:地市级,3:县级,4:镇级) |
| blng | string | 经度(百度坐标) |
| blat | string | 纬度(百度坐标) |
| policy_id | int | 关联政策ID |
| phone | string | 联系电话 |
| manager | string | 负责人 |
| investor | string | 投资者 |
| investor_phone | string | 投资者手机号 |
| expect_open_time | string | 预计开业时间 |
| position | string | 所处地段 |
| endpoint_area | string | 终端面积 |
| pay | string | 支付金额 |
| add_to_endpoint_id | int | 关联的终端ID |
| state | int | 当前状态 |
| next_state | int | 下一个状态 |
| application_year | int | 申请年份 |

#### 扩展信息字段
| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| pics | array | 装修前实景图片数组 |
| extend | object | 扩展信息对象 |
| code | int | 终端编码 |
| endpoint_id | int | 终端ID |
| endpoint_code | string | 终端代码 |
| created_at_formatted | string | 格式化的创建时间 |
| endpoint_type_name | string | 终端类型名称 |
| state_name | string | 状态名称 |

#### 工作流相关字段
| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| available_actions | array | 当前可执行的操作列表 |
| current_state_info | object | 当前状态详细信息 |
| workflow_info | object | 工作流信息 |

#### 物料数据字段 (material_data)
| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| material_list | array | 物料列表(结构化格式) |
| total_amount | float | 物料总金额 |
| raw_detail | string | 原始字符串格式的物料详情 |
| is_raw_format | bool | 是否为原始字符串格式 |

#### 回传数据字段 (postback_data)
| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| write_off_table | string | 批复表文件路径 |
| lease_contract | string | 租赁合同文件路径 |
| annual_rent | float | 年租金 |
| design_renderings | string | 设计效果图 |
| renovation_photos | string | 装修照片 |
| renovation_videos | string | 装修视频 |
| diploma | string | 资质证书 |
| endpoint_group_photo | string | 终端集体照 |
| extend | string | 扩展信息JSON字符串 |

#### 审核历史字段 (audit_history)
| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| id | int | 审核记录ID |
| action | string | 操作类型 |
| action_name | string | 操作名称 |
| from_state | int | 原状态 |
| to_state | int | 目标状态 |
| remark | string | 审核备注 |
| auditor_id | int | 审核人ID |
| auditor_name | string | 审核人姓名 |
| created_at | string | 审核时间 |

### 错误响应
```json
{
  "ok": 0,
  "msg": "申请不存在",
  "data": null
}
```

### 状态码说明
- `200`: 请求成功
- `404`: 申请不存在
- `500`: 服务器内部错误

### 备注
1. 物料数据支持两种格式：结构化格式和原始字符串格式
2. 当 `is_raw_format` 为 `true` 时，使用 `raw_detail` 字段
3. 当 `is_raw_format` 为 `false` 时，使用 `material_list` 字段
4. 部分字段（如回传数据、审核历史）可能为空，取决于申请的当前状态
5. 工作流相关字段提供当前可执行的操作和状态信息
