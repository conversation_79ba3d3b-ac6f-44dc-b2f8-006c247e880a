package processors

import (
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// RejectedProcessor 审核拒绝状态处理器
type RejectedProcessor struct{}

// NewRejectedProcessor 创建审核拒绝处理器
func NewRejectedProcessor() *RejectedProcessor {
	return &RejectedProcessor{}
}

// Process 处理审核拒绝状态 - 验证拒绝原因
func (p *RejectedProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 验证拒绝备注
	remark, exists := req.Data["remark"]
	if !exists || remark == "" {
		return errors.NewErr("审核不通过，备注不能为空")
	}

	// 拒绝时不需要创建终端，只需要验证备注即可
	return nil
}

// GetName 获取处理器名称
func (p *RejectedProcessor) GetName() string {
	return "rejected"
}
