package endpoint_application

import (
	"errors"
	"marketing/internal/api/endpoint_application"
	"marketing/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InstallmentDao interface {
	// GetInstallmentByID 根据ID获取分期记录
	GetInstallmentByID(ctx *gin.Context, id uint) (*model.EndpointApplicationInstallment, error)

	// GetInstallmentList 获取分期列表
	GetInstallmentList(ctx *gin.Context, req *endpoint_application.InstallmentListReq) ([]*model.EndpointApplicationInstallment, int64, error)

	// UpdateInstallment 更新分期记录
	UpdateInstallment(ctx *gin.Context, id uint, data map[string]interface{}) error

	// RecordAccount 入账操作
	RecordAccount(ctx *gin.Context, id uint, supportStatus uint8, recordedBy uint, remark string) error

	// GetInstallmentsByApplicationID 根据申请ID获取分期列表
	GetInstallmentsByApplicationID(ctx *gin.Context, applicationID uint) ([]*model.EndpointApplicationInstallment, error)

	// WithTransaction 事务操作
	WithTransaction(ctx *gin.Context, fn func(tx InstallmentDao) error) error

	// GetDB 获取数据库连接
	GetDB(ctx *gin.Context) *gorm.DB
}

type installmentDao struct {
	db *gorm.DB
}

// NewInstallmentDao 创建分期DAO实例
func NewInstallmentDao(db *gorm.DB) InstallmentDao {
	return &installmentDao{db: db}
}

// GetInstallmentByID 根据ID获取分期记录
func (d *installmentDao) GetInstallmentByID(ctx *gin.Context, id uint) (*model.EndpointApplicationInstallment, error) {
	var installment model.EndpointApplicationInstallment
	err := d.db.WithContext(ctx).Where("id = ?", id).First(&installment).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &installment, nil
}

// GetInstallmentList 获取分期列表
func (d *installmentDao) GetInstallmentList(ctx *gin.Context, req *endpoint_application.InstallmentListReq) ([]*model.EndpointApplicationInstallment, int64, error) {
	var installments []*model.EndpointApplicationInstallment
	var total int64

	query := d.db.WithContext(ctx).Model(&model.EndpointApplicationInstallment{})

	// 条件筛选
	if req.ApplicationID > 0 {
		query = query.Where("application_id = ?", req.ApplicationID)
	}
	if req.Year > 0 {
		query = query.Where("year = ?", req.Year)
	}
	if req.Month > 0 {
		query = query.Where("month = ?", req.Month)
	}
	if req.SupportStatus != nil {
		query = query.Where("support_status = ?", *req.SupportStatus)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&installments).Error; err != nil {
		return nil, 0, err
	}

	return installments, total, nil
}

// UpdateInstallment 更新分期记录
func (d *installmentDao) UpdateInstallment(ctx *gin.Context, id uint, data map[string]interface{}) error {
	return d.db.WithContext(ctx).Model(&model.EndpointApplicationInstallment{}).
		Where("id = ?", id).Updates(data).Error
}

// RecordAccount 入账操作
func (d *installmentDao) RecordAccount(ctx *gin.Context, id uint, supportStatus uint8, recordedBy uint, remark string) error {
	data := map[string]interface{}{
		"support_status": supportStatus,
		"recorded_by":    recordedBy,
		"recorded_at":    time.Now(),
		"record_remark":  remark,
	}
	return d.UpdateInstallment(ctx, id, data)
}

// GetInstallmentsByApplicationID 根据申请ID获取分期列表
func (d *installmentDao) GetInstallmentsByApplicationID(ctx *gin.Context, applicationID uint) ([]*model.EndpointApplicationInstallment, error) {
	var installments []*model.EndpointApplicationInstallment
	err := d.db.WithContext(ctx).Where("application_id = ?", applicationID).
		Order("date ASC").Find(&installments).Error
	return installments, err
}

// WithTransaction 事务操作
func (d *installmentDao) WithTransaction(ctx *gin.Context, fn func(tx InstallmentDao) error) error {
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txDao := &installmentDao{db: tx}
		return fn(txDao)
	})
}

// GetDB 获取数据库连接
func (d *installmentDao) GetDB(ctx *gin.Context) *gorm.DB {
	return d.db.WithContext(ctx)
}
