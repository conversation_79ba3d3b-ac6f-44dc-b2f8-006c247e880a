package processors

import (
	"fmt"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/sms"
	"marketing/internal/pkg/statemachine"
	"strings"

	"github.com/gin-gonic/gin"
)

// DesignNotificationProcessor 设计通知处理器
type DesignNotificationProcessor struct{}

// NewDesignNotificationProcessor 创建设计通知处理器
func NewDesignNotificationProcessor() *DesignNotificationProcessor {
	return &DesignNotificationProcessor{}
}

// Process 处理设计通知逻辑
func (p *DesignNotificationProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取配置仓库
	configRepo, ok := req.Data["configRepo"].(dao.ConfigRepository)
	if !ok {
		return nil
	}

	// 获取申请信息
	apply, exists := req.Data["application"]
	if !exists {
		return nil
	}

	applyModel, ok := apply.(*model.EndpointApplication)
	if !ok {
		return nil
	}

	// 获取终端代码
	endpointCode, exists := req.Data["endpoint_code"].(string)
	if !exists || endpointCode == "" {
		return nil
	}

	// 发送设计通知
	go func() {
		err := p.sendDesignNotification(ctx, configRepo, applyModel, endpointCode)
		if err != nil {

		}
	}()
	return nil
}

// sendDesignNotification 发送设计通知
func (p *DesignNotificationProcessor) sendDesignNotification(ctx *gin.Context, configRepo dao.ConfigRepository, apply *model.EndpointApplication, code string) error {
	// 根据申请类型生成不同的设计消息
	var designMsg string
	switch apply.ApplyType {
	case "new":
		designMsg = fmt.Sprintf("终端 %s 建店申请审核已通过，请进入营销后台查看设计需求。", code)
	case "renew":
		designMsg = fmt.Sprintf("终端 %s 老店改造申请审核已通过，请进入营销后台查看设计需求。", code)
	case "upgrade":
		designMsg = fmt.Sprintf("终端 %s 终端形象升级申请审核已通过，请进入营销后台查看设计需求。", code)
	default:
		designMsg = fmt.Sprintf("终端 %s 申请审核已通过，请进入营销后台查看设计需求。", code)
	}

	// 添加链接
	linkText := "营销后台"
	linkURL := "https://yx.readboy.com/admin/auth/login"
	content := strings.Replace(designMsg, linkText, fmt.Sprintf(`<a href="%s">%s</a>`, linkURL, linkText), 1)

	// 获取设计人员配置
	design, err := configRepo.GetConfig(ctx, "endpoint_audit_pass_notify_employess")
	if err != nil {
		log.Error(fmt.Sprintf("获取审核通过通知设计人员配置失败: %s", err.Error()))
		return err
	}

	if design == "" {
		log.Info("未配置设计人员通知列表，跳过设计通知")
		return nil
	}

	// 发送企微消息给设计人员
	designEmployees := strings.Split(design, ",")
	err = sms.OaSendWecomMsg(designEmployees, content)
	if err != nil {
		log.Error(fmt.Sprintf("发送设计通知失败: 终端代码=%s, 设计人员=%s, 错误=%s", code, design, err.Error()))
		return err
	}

	log.Info(fmt.Sprintf("设计通知发送成功: 终端代码=%s, 设计人员=%s", code, design))
	return nil
}

// GetName 获取处理器名称
func (p *DesignNotificationProcessor) GetName() string {
	return "design_notification"
}
