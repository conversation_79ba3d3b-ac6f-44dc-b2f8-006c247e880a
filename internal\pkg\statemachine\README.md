# 状态机包 (State Machine Package)

## 概述

这是一个简化的状态机包，专门用于处理常规建店工作流。移除了复杂的政策类型(PolicyType)支持，只保留核心的状态转换功能。

## 主要特性

- **简化设计**: 不再支持多种政策类型，只使用一个标准工作流
- **内置工作流**: 预定义了常规建店工作流，无需外部配置
- **验证器支持**: 内置多种验证器确保状态转换的合法性
- **处理器支持**: 内置多种处理器处理状态转换时的业务逻辑
- **易于使用**: 简单的API接口，易于集成

## 工作流状态

标准建店工作流包含以下状态：

| 状态ID | 状态名称 | 状态类型 | 描述 |
|--------|----------|----------|------|
| 0 | 待审核 | audit | 初始状态，等待审核 |
| 100 | 审核通过 | material | 审核通过，可申请物料支持 |
| 200 | 物料支持完成 | material_supported | 物料支持完成，等待回传建店资料 |
| 300 | 核销已回传，待初审 | write_off | 建店资料已回传，等待初审 |
| 400 | 初审通过,待渠道审核 | channel_audit | 初审通过，等待渠道审核 |
| 500 | 渠道审核通过 | completed | 最终完成状态 |
| -100 | 审核不通过 | rejected | 审核被拒绝 |
| -400 | 核销初审不通过 | rejected | 核销初审被拒绝 |
| -500 | 渠道审核不通过 | rejected | 渠道审核被拒绝 |
| -900 | 建店终止 | canceled | 建店流程被取消 |

## 可用操作

### 状态0 (待审核)
- `approve`: 审核通过
- `reject`: 审核拒绝

### 状态100 (审核通过)
- `submit_material`: 申请物料支持

### 状态200 (物料支持完成)
- `complete_store`: 回传建店资料

### 状态300 (核销已回传，待初审)
- `approve`: 初审通过
- `reject`: 初审拒绝

### 状态400 (初审通过,待渠道审核)
- `approve`: 渠道审核通过
- `reject`: 渠道审核拒绝

## 使用方法

### 1. 创建状态机实例

```go
import "marketing/internal/pkg/statemachine"

// 创建状态机实例（自动加载标准工作流和注册验证器/处理器）
sm := statemachine.NewEndpointApplicationStateMachine()
```

### 2. 执行状态转换

```go
import (
    "marketing/internal/consts"
    "github.com/gin-gonic/gin"
)

// 创建状态转换请求
req := &statemachine.StateTransitionRequest{
    ApplicationID: 123,
    CurrentState:  0, // 待审核
    Action:        "approve",
    Data: map[string]interface{}{
        "audit_user_id": 456,
        "audit_remark":  "申请材料齐全，审核通过",
    },
}

// 执行状态转换
ctx := &gin.Context{} // 实际使用中应该是真实的gin上下文
err := sm.ExecuteTransition(ctx, req)
if err != nil {
    // 处理错误
    log.Printf("状态转换失败: %v", err)
    return
}

// 转换成功，req.TargetState 包含新状态
log.Printf("状态转换成功: %d -> %d", req.CurrentState, req.TargetState)
```

### 3. 获取可用操作

```go
actions, err := sm.GetAvailableActions(consts.EndpointApplicationState(100))
if err != nil {
    log.Printf("获取可用操作失败: %v", err)
    return
}

for _, action := range actions {
    fmt.Printf("操作: %s - %s\n", action.Type, action.Label)
}
```

### 4. 获取下一状态

```go
nextStates, err := sm.GetNextStates(consts.EndpointApplicationState(100))
if err != nil {
    log.Printf("获取下一状态失败: %v", err)
    return
}

fmt.Printf("可能的下一状态: %v\n", nextStates)
```

### 5. 获取状态信息

```go
stateInfo, err := sm.GetStateInfo(consts.EndpointApplicationState(100))
if err != nil {
    log.Printf("获取状态信息失败: %v", err)
    return
}

fmt.Printf("状态信息: ID=%d, Name=%s, Type=%s\n", 
    stateInfo.ID, stateInfo.Name, stateInfo.Type)
```

## 验证器

内置的验证器包括：

- `basic_validator`: 基础验证（ApplicationID、Action不能为空）
- `audit_validator`: 审核验证（拒绝时需要备注）
- `material_validator`: 物料验证
- `agency_validator`: 代理商权限验证
- `file_validator`: 文件验证
- `amount_validator`: 金额验证
- `installment_validator`: 分期验证

## 处理器

内置的处理器包括：

- `audit_processor`: 审核处理器
- `material_processor`: 物料处理器
- `writeoff_processor`: 核销处理器
- `endpoint_creator`: 终端创建处理器
- `notification_processor`: 通知处理器
- `completion_processor`: 完成处理器
- `state_log_processor`: 状态日志处理器

## 注意事项

1. 状态机实例创建后会自动加载标准工作流配置
2. 所有验证器和处理器都会自动注册
3. 状态转换时会按顺序执行验证器和处理器
4. 如果任何验证器失败，状态转换会被阻止
5. 处理器用于执行状态转换时的业务逻辑（如更新数据库、发送通知等）

## 示例代码

详细的使用示例请参考 `example.go` 文件。
