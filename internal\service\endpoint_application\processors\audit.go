package processors

import (
	"strconv"
	"time"

	endpointDao "marketing/internal/dao/endpoint"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// AuditProcessor 审核处理器（同时处理通过/不通过）
type AuditProcessor struct{}

// NewAuditProcessor 创建审核处理器
func NewAuditProcessor() *AuditProcessor { return &AuditProcessor{} }

// Process 根据动作/目标状态分支处理
func (p *AuditProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	action := req.Action // 约定: "approve" | "reject"

	// 处理人
	auditMan := ctx.GetUint("real_name")

	updateData["audit_man"] = auditMan
	updateData["audit_time"] = time.Now()
	remark, exists := req.Data["audit_advice"]
	updateData["audit_advice"] = remark
	if action == "reject" {
		// 审核不通过：校验备注
		if !exists || remark == "" {
			return errors.NewErr("审核不通过，备注不能为空")
		}
		return nil
	}

	// 默认按通过处理：创建终端、落库关联
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}

	applyVal, exists := req.Data["application"]
	if !exists {
		return errors.NewErr("缺少申请信息")
	}
	apply, ok := applyVal.(*model.EndpointApplication)
	if !ok {
		return errors.NewErr("申请信息格式错误")
	}

	endpointInfo, err := p.createEndpoint(ctx, txRepo, apply)
	if err != nil {
		return err
	}
	if endpointInfo != nil {
		req.Data["endpoint"] = endpointInfo
		req.Data["endpoint_code"] = endpointInfo.Code
		updateData["add_to_endpoint_id"] = endpointInfo.ID
	}

	return nil
}

// createEndpoint 创建终端信息（从 ApprovedProcessor 复用逻辑）
func (p *AuditProcessor) createEndpoint(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, apply *model.EndpointApplication) (*model.Endpoint, error) {
	address := apply.Address
	if apply.Province > 0 {
		provinceStr := strconv.Itoa(apply.Province)
		cityStr := strconv.Itoa(apply.City)
		districtStr := strconv.Itoa(apply.District)
		address = provinceStr + cityStr + districtStr + apply.Address
	}

	txDB := txRepo.GetDB(ctx)
	txEndpointDao := endpointDao.NewEndpointDao(txDB)

	endpointInfo, err := txEndpointDao.CreateEndpoint(ctx, &model.Endpoint{
		Name:         apply.Name,
		Type:         int8(apply.Type),
		Province:     apply.Province,
		City:         apply.City,
		District:     apply.District,
		Address:      address,
		TopAgency:    int(apply.TopAgency),
		Phone:        apply.Phone,
		Manager:      &apply.Manager,
		Blng:         &apply.Blng,
		Blat:         &apply.Blat,
		Lng:          apply.Lng,
		Lat:          apply.Lat,
		SecondAgency: int(apply.SecondAgency),
		ChannelLevel: apply.ChannelLevel,
	})
	return endpointInfo, err
}

// GetName 名称（不直接使用，一般通过配置映射）
func (p *AuditProcessor) GetName() string { return "audit" }
