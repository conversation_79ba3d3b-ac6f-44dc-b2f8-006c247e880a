# 终端政策工作流模板字段说明

## 概述

为终端政策添加了工作流模板关联字段，允许每个政策指定使用特定的工作流模板，提供更灵活的业务流程配置。

## 数据库变更

### 新增字段

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| workflow_template | int(11) | 0 | 关联的工作流模板ID，0表示使用默认工作流 |

### 索引

- `idx_workflow_template`: 为 `workflow_template` 字段创建索引，提高查询性能

## API 变更

### 创建终端政策 API

**请求路径**: `POST /admin/endpoint-policies`

**新增请求参数**:
```json
{
  "workflow_template": 1  // 工作流模板ID，可选，默认为0（使用默认工作流）
}
```

### 更新终端政策 API

**请求路径**: `PUT /admin/endpoint-policies/{id}`

**新增请求参数**:
```json
{
  "workflow_template": 1  // 工作流模板ID，可选
}
```

### 政策详情/列表响应

**新增响应字段**:
```json
{
  "workflow_template": 1  // 关联的工作流模板ID
}
```

## 使用说明

### 1. 默认行为

- 当 `workflow_template` 为 0 时，使用系统默认的标准工作流
- 新创建的政策如果不指定工作流模板，默认使用标准工作流

### 2. 指定工作流模板

- 可以通过 `workflow_template` 字段指定特定的工作流模板ID
- 系统会验证指定的工作流模板是否存在
- 如果指定的工作流模板不存在，会返回错误

### 3. 工作流模板管理

- 工作流模板通过独立的工作流模板管理API进行管理
- 可以通过 `GET /admin/workflow-templates` 获取可用的工作流模板列表
- 每个工作流模板都有唯一的ID和slug标识

## 验证规则

### 创建政策时

1. 如果指定了 `workflow_template` 且值大于0，系统会验证该工作流模板是否存在
2. 如果工作流模板不存在，返回错误："指定的工作流模板不存在"
3. 如果未指定或值为0，使用默认工作流

### 更新政策时

1. 同创建时的验证规则
2. 可以将工作流模板从指定值改为0（使用默认工作流）
3. 可以在不同的工作流模板之间切换

## 示例

### 创建使用默认工作流的政策

```json
POST /admin/endpoint-policies
{
  "name": "标准建店政策",
  "description": "使用默认工作流的标准政策",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "enabled": 1
  // workflow_template 不指定，默认为0
}
```

### 创建使用特定工作流的政策

```json
POST /admin/endpoint-policies
{
  "name": "特殊建店政策",
  "description": "使用自定义工作流的特殊政策",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "workflow_template": 2,  // 指定工作流模板ID
  "enabled": 1
}
```

### 更新政策的工作流模板

```json
PUT /admin/endpoint-policies/123
{
  "workflow_template": 3  // 切换到工作流模板ID为3的模板
}
```

### 响应示例

```json
{
  "id": 123,
  "name": "特殊建店政策",
  "description": "使用自定义工作流的特殊政策",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "workflow_template": 2,  // 关联的工作流模板ID
  "enabled": 1,
  "created_at": "2024-09-05 10:00:00",
  "updated_at": "2024-09-05 10:00:00"
}
```

## 注意事项

1. **向后兼容**: 现有的政策会自动设置 `workflow_template` 为0，继续使用默认工作流
2. **数据一致性**: 删除工作流模板时，需要检查是否有政策在使用该模板
3. **性能考虑**: 为 `workflow_template` 字段创建了索引，提高查询性能
4. **业务逻辑**: 工作流模板的变更不会影响已经在流程中的申请，只对新申请生效

## 相关API

- `GET /admin/workflow-templates` - 获取工作流模板列表
- `GET /admin/workflow-templates/{id}` - 获取工作流模板详情
- `POST /admin/workflow-templates` - 创建工作流模板
- `PUT /admin/workflow-templates/{id}` - 更新工作流模板
- `DELETE /admin/workflow-templates/{id}` - 删除工作流模板
