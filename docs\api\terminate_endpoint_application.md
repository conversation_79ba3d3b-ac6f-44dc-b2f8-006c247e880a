# 建店终止 API 文档

## 概述

建店终止接口用于终止建店申请，**可在任何状态下执行，不受工作流限制**。这是一个特殊的管理操作，用于紧急情况下强制终止申请。

## 接口信息

**URL:** `PUT /admin/endpoint-application/{id}/terminate`

**方法:** PUT

**认证:** 需要管理员权限

## 请求参数

### 路径参数
- `id` (必填) - 建店申请ID，类型：uint

### 请求体参数
```json
{
  "terminate_reason": "string",  // 必填，终止原因
  "remark": "string",           // 可选，备注
  "extend": {                   // 可选，扩展字段
    "custom_field": "value"
  }
}
```

#### 参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| terminate_reason | string | 是 | 终止原因，不能为空 |
| remark | string | 否 | 备注信息 |
| extend | object | 否 | 扩展字段，用于存储额外信息 |

## 响应格式

### 成功响应
```json
{
  "ok": 1,
  "msg": "ok"
}
```

### 错误响应
```json
{
  "ok": 0,
  "msg": "错误信息"
}
```

## 使用示例

### 1. 基本终止请求

```bash
curl -X PUT "http://api.example.com/admin/endpoint-application/123/terminate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "terminate_reason": "客户要求取消",
    "remark": "客户因资金问题要求终止建店申请"
  }'
```

### 2. 带扩展字段的终止请求

```bash
curl -X PUT "http://api.example.com/admin/endpoint-application/123/terminate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "terminate_reason": "政策变更",
    "remark": "总部政策调整，暂停该区域建店",
    "extend": {
      "operator_id": 1001,
      "department": "市场部",
      "approval_level": "区域经理"
    }
  }'
```

## 状态流转

建店终止操作具有以下特点：
- **不受状态限制**: 可在任何状态下执行（待审核、已批准、物料支持等）
- **目标状态**: `ApplicationCancelled` (-900) - 已终止
- **绕过工作流**: 直接更新数据库，不通过状态机验证

## 业务逻辑

### 直接终止处理
终止操作直接在服务层处理，**绕过工作流限制**：

1. **申请验证**
   - 验证申请是否存在
   - 验证是否已处于终止状态

2. **数据验证**
   - 验证终止原因不能为空
   - 验证申请ID的有效性

3. **直接数据更新**
   - 设置 `state` = `ApplicationCancelled` (-900)
   - 设置 `next_state` = 0 (无下一状态)
   - 设置 `terminate_time` - 终止时间
   - 设置 `terminate_reason` - 终止原因
   - 设置 `remark` - 备注信息（如有）
   - 设置 `extend` - 扩展字段（如有）

4. **状态记录**
   - 创建状态转换记录到 `endpoint_application_status` 表
   - 记录操作人、原状态、新状态等信息

### 绕过工作流的处理流程

```mermaid
graph TD
    A[终止请求] --> B[验证申请存在]
    B --> C[验证终止原因]
    C --> D[检查是否已终止]
    D --> E[直接更新数据库]
    E --> F[创建状态记录]
    F --> G[返回成功响应]
    
    style E fill:#ff9999
    style F fill:#ff9999
    
    H[任何状态] --> A
    I[待审核] --> A
    J[已批准] --> A
    K[物料支持] --> A
    L[建店回传] --> A
```

## 常见错误

### 1. 参数验证错误
```json
{
  "ok": 0,
  "msg": "terminate_reason不能为空"
}
```

### 2. 申请不存在
```json
{
  "ok": 0,
  "msg": "终端申请不存在"
}
```

### 3. 已处于终止状态
```json
{
  "ok": 0,
  "msg": "申请已处于终止状态"
}
```

### 4. 权限不足
```json
{
  "ok": 0,
  "msg": "权限不足"
}
```

## 注意事项

1. **权限要求**: 只有具备管理员权限的用户才能执行终止操作
2. **无状态限制**: ⚠️ **任何状态的申请都可以终止**，包括已完成的申请
3. **不可逆操作**: 终止操作是不可逆的，请谨慎操作
4. **绕过工作流**: 终止操作直接修改数据库，不经过工作流验证
5. **紧急使用**: 主要用于紧急情况或特殊业务需求
6. **数据保留**: 终止后的申请数据会被保留，便于后续查询和审计
7. **审计记录**: 所有终止操作都会记录到状态转换表中
8. **扩展性**: 可通过 `extend` 字段存储额外的业务信息

## 相关接口

- `GET /admin/endpoint-application/{id}/available-actions` - 获取可用操作
- `GET /admin/endpoint-application/{id}/next-states` - 获取下一状态
- `POST /admin/endpoint-application/{id}/transition` - 通用状态转换
- `POST /admin/workflow/execute` - 通用工作流执行

## 版本历史

- v1.0 - 初始版本，支持基本的建店终止功能
