package statemachine

import (
	"marketing/internal/consts"

	"github.com/gin-gonic/gin"
)

// WorkflowConfig 工作流配置
type WorkflowConfig struct {
	Name        string                          `json:"name"`
	Slug        string                          `json:"slug"`
	StartState  consts.EndpointApplicationState `json:"start_state"`
	States      map[string]*StateNode           `json:"states"`
	GlobalRules []BusinessRule                  `json:"global_rules"`
	Validators  []string                        `json:"validators"`  // 验证器名称列表
	Processors  []string                        `json:"processors"`  // 处理器名称列表
}

// StateNode 状态节点
type StateNode struct {
	ID          consts.EndpointApplicationState            `json:"id"`
	Name        string                                     `json:"name"`
	Type        string                                     `json:"type"` // audit, material, writeoff, postback
	Conditions  []Condition                                `json:"conditions"`
	Actions     []Action                                   `json:"actions"`
	Transitions map[string]consts.EndpointApplicationState `json:"transitions"`
	Validators  []string                                   `json:"validators"`
	Processors  []string                                   `json:"processors"`
}

// Condition 条件
type Condition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, gt, lt, in, not_in
	Value    interface{} `json:"value"`
}

// Action 动作
type Action struct {
	Type   string                 `json:"type"`
	Label  string                 `json:"label"`
	Config map[string]interface{} `json:"config"`
}

// BusinessRule 业务规则
type BusinessRule struct {
	Condition string `json:"condition"`
	Action    string `json:"action"`
	Message   string `json:"message"`
}

// StateTransitionRequest 状态转换请求
type StateTransitionRequest struct {
	ApplicationID uint                            `json:"application_id"`
	CurrentState  consts.EndpointApplicationState `json:"current_state"`
	TargetState   consts.EndpointApplicationState `json:"target_state"`
	Action        string                          `json:"action"`
	Data          map[string]interface{}          `json:"data"`
	Context       *gin.Context                    `json:"-"`
}

// Validator 验证器接口
type Validator interface {
	Validate(ctx *gin.Context, req *StateTransitionRequest) error
	GetName() string
}

// Processor 处理器接口
type Processor interface {
	Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error
	GetName() string
}

// ValidatorProvider 验证器提供者接口
type ValidatorProvider interface {
	GetValidator(name string) (Validator, error)
	ListValidators() []string
}

// ProcessorProvider 处理器提供者接口
type ProcessorProvider interface {
	GetProcessor(name string) (Processor, error)
	ListProcessors() []string
}

// StateMachine 状态机接口
type StateMachine interface {
	ExecuteTransition(ctx *gin.Context, req *StateTransitionRequest) error
	GetAvailableActions(state consts.EndpointApplicationState) ([]Action, error)
	GetNextStates(state consts.EndpointApplicationState) ([]consts.EndpointApplicationState, error)
	GetStateInfo(state consts.EndpointApplicationState) (*StateNode, error)
	GetWorkflowInfo() *WorkflowConfig
	RegisterValidator(validator Validator)
	RegisterProcessor(processor Processor)
	LoadWorkflow(config *WorkflowConfig) error
}
