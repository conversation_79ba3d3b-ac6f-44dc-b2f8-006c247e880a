package types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/spf13/viper"
)

var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)

// OssPath OSS文件路径类型，自动处理相对路径存储和完整URL输出
type OssPath string

// MarshalJSON 实现 json.Marshaler 接口，输出时自动拼接完整URL
func (o OssPath) MarshalJSON() ([]byte, error) {
	if string(o) == "" {
		return []byte(`""`), nil
	}
	fullUrl := o.getFullUrl()
	return json.Marshal(fullUrl)
}

// UnmarshalJSON 实现 json.Unmarshaler 接口，输入时自动截取相对路径
func (o *OssPath) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*o = OssPath(stripOssBaseUrl(s))
	return nil
}

// Value 实现 driver.Valuer 接口，数据库存储相对路径
func (o OssPath) Value() (driver.Value, error) {
	if string(o) == "" {
		return nil, nil
	}
	return string(o), nil
}

// Scan 实现 sql.Scanner 接口，从数据库读取
func (o *OssPath) Scan(value interface{}) error {
	if value == nil {
		*o = ""
		return nil
	}

	switch v := value.(type) {
	case string:
		*o = OssPath(v)
		return nil
	case []byte:
		*o = OssPath(string(v))
		return nil
	}

	return fmt.Errorf("cannot convert %T to OssPath", value)
}

// String 实现 Stringer 接口，返回完整URL
func (o OssPath) String() string {
	if string(o) == "" {
		return ""
	}
	return o.getFullUrl()
}

// Set 设置值，自动截取相对路径
func (o *OssPath) Set(value interface{}) (old interface{}) {
	oldValue := *o
	strValue := convertToString(value)
	*o = OssPath(stripOssBaseUrl(strValue))
	return oldValue
}

// GetRelativePath 获取相对路径（数据库存储的原始值）
func (o OssPath) GetRelativePath() string {
	return string(o)
}

// GetFullUrl 获取完整URL
func (o OssPath) GetFullUrl() string {
	return o.getFullUrl()
}

// getFullUrl 内部方法，获取完整URL
func (o OssPath) getFullUrl() string {
	path := string(o)
	oldUrl := viper.GetString("oss.oldUrl")
	if strings.HasPrefix(path, "rbcare/") {
		return fillUpUrl(oldUrl, path)
	}
	newUrl := viper.GetString("oss.url")
	return fillUpUrl(newUrl, path)
}

// convertToString 将任意类型转换为字符串
func convertToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case fmt.Stringer:
		return v.String()
	default:
		return fmt.Sprintf("%v", v)
	}
}

// stripOssBaseUrl 截取OSS基础URL，保留相对路径
func stripOssBaseUrl(url string) string {
	if url == "" {
		return url
	}

	// 如果不是HTTP/HTTPS URL，直接返回
	if !httpPrefixReg.MatchString(url) {
		return url
	}

	// 获取配置中的OSS基础URL列表
	baseUrls := []string{
		viper.GetString("oss.url"),
		viper.GetString("oss.oldUrl"),
	}

	// 尝试移除各种可能的基础URL前缀
	for _, baseUrl := range baseUrls {
		if baseUrl != "" && strings.HasPrefix(url, baseUrl) {
			return strings.TrimPrefix(url, baseUrl)
		}
	}

	return url
}

// fillUpUrl 拼接完整的URL
func fillUpUrl(prefix, url string) string {
	if url == "" {
		return prefix
	}

	// 如果已经是完整的HTTP/HTTPS URL，直接返回
	if httpPrefixReg.MatchString(url) {
		return url
	}

	// 确保前缀以/结尾，URL不以/开头
	prefix = strings.TrimRight(prefix, "/")
	url = strings.TrimLeft(url, "/")

	return prefix + "/" + url
}

// UnmarshalJSONField 通用的 JSON 字段解析方法
// 参考 unmarshalField 的实现，用于解析 JSON 字符串到目标类型
func UnmarshalJSONField(src string, dest any) {
	if src == "" {
		return
	}

	if err := json.Unmarshal([]byte(src), dest); err != nil {
		// 使用 zap 记录错误日志
		fmt.Printf("解析 JSON 字段失败: %v, src: %s\n", err, src)
	}
}

// UnmarshalJSONFieldSafe 安全的 JSON 字段解析方法，返回错误而不记录日志
func UnmarshalJSONFieldSafe(src string, dest any) error {
	if src == "" {
		return nil
	}
	return json.Unmarshal([]byte(src), dest)
}

// UnmarshalJSONFieldWithDefault 带默认值的 JSON 字段解析方法
func UnmarshalJSONFieldWithDefault(src string, dest any, defaultValue any) {
	if src == "" {
		// 如果源字符串为空，设置默认值
		if defaultValue != nil {
			// 这里需要根据具体类型进行转换，简化处理
			switch d := dest.(type) {
			case *[]string:
				if defaultSlice, ok := defaultValue.([]string); ok {
					*d = defaultSlice
				} else {
					*d = []string{}
				}
			case *[]OssPath:
				if defaultSlice, ok := defaultValue.([]OssPath); ok {
					*d = defaultSlice
				} else {
					*d = []OssPath{}
				}
			case *map[string]any:
				if defaultMap, ok := defaultValue.(map[string]any); ok {
					*d = defaultMap
				} else {
					*d = map[string]any{}
				}
			}
		}
		return
	}

	if err := json.Unmarshal([]byte(src), dest); err != nil {
		fmt.Printf("解析 JSON 字段失败，使用默认值: %v, src: %s\n", err, src)
		// 解析失败时设置默认值
		if defaultValue != nil {
			switch d := dest.(type) {
			case *[]string:
				if defaultSlice, ok := defaultValue.([]string); ok {
					*d = defaultSlice
				} else {
					*d = []string{}
				}
			case *[]OssPath:
				if defaultSlice, ok := defaultValue.([]OssPath); ok {
					*d = defaultSlice
				} else {
					*d = []OssPath{}
				}
			case *map[string]any:
				if defaultMap, ok := defaultValue.(map[string]any); ok {
					*d = defaultMap
				} else {
					*d = map[string]any{}
				}
			}
		}
	}
}
