package workflow

import (
	"fmt"

	endpointApplicationApi "marketing/internal/api/endpoint_application"
	workflowApi "marketing/internal/api/workflow"
	"marketing/internal/pkg/errors"
	endpointApplicationSvc "marketing/internal/service/endpoint_application"

	"github.com/gin-gonic/gin"
)

// WorkflowService 通用工作流服务接口
type WorkflowService interface {
	// ExecuteWorkflow 执行工作流
	ExecuteWorkflow(ctx *gin.Context, req *workflowApi.WorkflowExecuteReq) (*workflowApi.WorkflowExecuteResp, error)

	// GetWorkflowState 获取工作流状态
	GetWorkflowState(ctx *gin.Context, req *workflowApi.GetWorkflowStateReq) (*workflowApi.GetWorkflowStateResp, error)

	// GetAvailableActions 获取可用操作
	GetAvailableActions(ctx *gin.Context, workflowType string, entityID uint) ([]workflowApi.ActionInfo, error)
}

// workflowService 工作流服务实现
type workflowService struct {
	endpointApplicationSvc endpointApplicationSvc.EndpointApplyService
}

// NewWorkflowService 创建工作流服务
func NewWorkflowService(
	endpointApplicationSvc endpointApplicationSvc.EndpointApplyService,
) WorkflowService {
	return &workflowService{
		endpointApplicationSvc: endpointApplicationSvc,
	}
}

// ExecuteWorkflow 执行工作流
func (s *workflowService) ExecuteWorkflow(ctx *gin.Context, req *workflowApi.WorkflowExecuteReq) (*workflowApi.WorkflowExecuteResp, error) {
	switch req.WorkflowType {
	case "endpoint_application":
		return s.executeEndpointApplicationWorkflow(ctx, req)
	case "warranty":
		// 可以后续添加保修工作流
		return nil, errors.NewErr("保修工作流暂未实现")
	case "reimbursement":
		// 可以后续添加报销工作流
		return nil, errors.NewErr("报销工作流暂未实现")
	default:
		return nil, errors.NewErr(fmt.Sprintf("不支持的工作流类型: %s", req.WorkflowType))
	}
}

// executeEndpointApplicationWorkflow 执行终端申请工作流
func (s *workflowService) executeEndpointApplicationWorkflow(ctx *gin.Context, req *workflowApi.WorkflowExecuteReq) (*workflowApi.WorkflowExecuteResp, error) {
	// 获取申请详情，用于获取当前状态
	applyDetail, err := s.endpointApplicationSvc.GetEndpointApplyDetail(ctx, req.EntityID)
	if err != nil {
		return nil, err
	}
	if applyDetail == nil {
		return nil, errors.NewErr("终端申请不存在")
	}

	beforeState := applyDetail.State

	// 构建状态转换请求
	stateTransitionReq := &endpointApplicationApi.StateTransitionReq{
		ID:     req.EntityID,
		Action: req.Action,
		Data:   req.Data,
	}

	// 添加备注和扩展字段到数据中
	if req.Remark != "" {
		stateTransitionReq.Data["remark"] = req.Remark
	}
	if req.Extend != nil {
		stateTransitionReq.Data["extend"] = req.Extend
	}

	// 执行状态转换
	err = s.endpointApplicationSvc.TransitionState(ctx, stateTransitionReq)
	if err != nil {
		return &workflowApi.WorkflowExecuteResp{
			Success:     false,
			Message:     err.Error(),
			BeforeState: beforeState,
			AfterState:  beforeState, // 失败时状态不变
		}, nil
	}

	// 重新获取申请信息，获取转换后的状态
	updatedApplyDetail, err := s.endpointApplicationSvc.GetEndpointApplyDetail(ctx, req.EntityID)
	if err != nil {
		return nil, err
	}

	return &workflowApi.WorkflowExecuteResp{
		Success:     true,
		Message:     "操作成功",
		BeforeState: beforeState,
		AfterState:  updatedApplyDetail.State,
		NextState:   updatedApplyDetail.NextState,
		Data:        stateTransitionReq.Data,
	}, nil
}

// GetWorkflowState 获取工作流状态
func (s *workflowService) GetWorkflowState(ctx *gin.Context, req *workflowApi.GetWorkflowStateReq) (*workflowApi.GetWorkflowStateResp, error) {
	switch req.WorkflowType {
	case "endpoint_application":
		return s.getEndpointApplicationWorkflowState(ctx, req)
	default:
		return nil, errors.NewErr(fmt.Sprintf("不支持的工作流类型: %s", req.WorkflowType))
	}
}

// getEndpointApplicationWorkflowState 获取终端申请工作流状态
func (s *workflowService) getEndpointApplicationWorkflowState(ctx *gin.Context, req *workflowApi.GetWorkflowStateReq) (*workflowApi.GetWorkflowStateResp, error) {
	// 获取申请详情
	applyDetail, err := s.endpointApplicationSvc.GetEndpointApplyDetail(ctx, req.EntityID)
	if err != nil {
		return nil, err
	}
	if applyDetail == nil {
		return nil, errors.NewErr("终端申请不存在")
	}

	// 获取可用操作
	actions, err := s.GetAvailableActions(ctx, req.WorkflowType, req.EntityID)
	if err != nil {
		return nil, err
	}

	return &workflowApi.GetWorkflowStateResp{
		CurrentState:     applyDetail.State,
		NextState:        applyDetail.NextState,
		AvailableActions: actions,
		WorkflowType:     req.WorkflowType,
		EntityID:         req.EntityID,
	}, nil
}

// GetAvailableActions 获取可用操作
func (s *workflowService) GetAvailableActions(ctx *gin.Context, workflowType string, entityID uint) ([]workflowApi.ActionInfo, error) {
	switch workflowType {
	case "endpoint_application":
		return s.getEndpointApplicationAvailableActions(ctx, entityID)
	default:
		return nil, errors.NewErr(fmt.Sprintf("不支持的工作流类型: %s", workflowType))
	}
}

// getEndpointApplicationAvailableActions 获取终端申请可用操作
func (s *workflowService) getEndpointApplicationAvailableActions(ctx *gin.Context, entityID uint) ([]workflowApi.ActionInfo, error) {
	// 获取可用操作（复用现有的GetAvailableActions方法）
	availableActions, err := s.endpointApplicationSvc.GetAvailableActions(ctx, entityID)
	if err != nil {
		return nil, err
	}

	// 转换为通用格式
	var actions []workflowApi.ActionInfo
	for _, action := range availableActions {
		// 从Config中提取额外信息
		name := action.Label
		if nameVal, exists := action.Config["name"]; exists {
			if nameStr, ok := nameVal.(string); ok {
				name = nameStr
			}
		}

		description := ""
		if descVal, exists := action.Config["description"]; exists {
			if descStr, ok := descVal.(string); ok {
				description = descStr
			}
		}

		targetState := 0
		if targetVal, exists := action.Config["target_state"]; exists {
			if targetInt, ok := targetVal.(int); ok {
				targetState = targetInt
			}
		}

		actions = append(actions, workflowApi.ActionInfo{
			Type:        action.Type,
			Name:        name,
			Description: description,
			TargetState: targetState,
			Data:        action.Config,
		})
	}

	return actions, nil
}
