# 工作流 extend 字段实现方案

## 🎯 实现目标

1. **状态转换日志的 extend 字段只存储表单提交的字段**
2. **从 extend 中获取相关字段，直接赋值给对应的结构体字段**
3. **使用状态的绝对值判断是否获取前面阶段的字段**

## 🔧 核心实现

### 1. 绝对值判断函数

```go
// abs 返回整数的绝对值
func abs(x int) int {
    if x < 0 {
        return -x
    }
    return x
}
```

### 2. 数据映射逻辑

```go
// 创建状态到审核记录的映射，并解析提交的数据
stateAuditMap := make(map[int]*api.StatusRecordWithUser)
stateExtendDataMap := make(map[int]map[string]interface{})

for _, record := range auditHistory {
    stateAuditMap[record.AfterState] = record
    
    // 解析 extend 字段中的表单提交数据
    if record.Extend != "" {
        var extendData map[string]interface{}
        if err := json.Unmarshal([]byte(record.Extend), &extendData); err == nil {
            stateExtendDataMap[record.AfterState] = extendData
        }
    }
}
```

### 3. 各阶段数据构建

#### 审核阶段 (状态 ±100)
```go
// 1. 审核数据 - 使用绝对值判断状态
if abs(apply.State) >= 100 {
    // 获取审核记录（优先获取正状态，如果没有则获取负状态）
    var auditRecord *api.StatusRecordWithUser
    var extendData map[string]interface{}
    
    if record, exists := stateAuditMap[100]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[100]
    } else if record, exists := stateAuditMap[-100]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[-100]
    }
    
    if auditRecord != nil {
        // 设置审核人和审核时间
        if detail.Audit.Extend == nil {
            detail.Audit.Extend = make(map[string]any)
        }
        detail.Audit.Extend["auditor_name"] = auditRecord.HandlerName
        detail.Audit.Extend["audit_time"] = auditRecord.CreatedAt
        _, actionName := e.inferActionFromStateChange(auditRecord.BeforeState, auditRecord.AfterState)
        detail.Audit.Extend["audit_action"] = actionName
        
        // 从 extend 中获取表单提交的字段
        if extendData != nil {
            if auditAdvice, exists := extendData["audit_advice"]; exists {
                if adviceStr, ok := auditAdvice.(string); ok {
                    detail.Audit.AuditAdvice = adviceStr
                }
            }
        }
    }
}
```

#### 物料阶段 (状态 ±200)
```go
// 2. 物料数据 - 使用绝对值判断状态
if abs(apply.State) >= 200 {
    var auditRecord *api.StatusRecordWithUser
    var extendData map[string]interface{}
    
    if record, exists := stateAuditMap[200]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[200]
    } else if record, exists := stateAuditMap[-200]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[-200]
    }
    
    if auditRecord != nil {
        detail.Material.AuditorName = auditRecord.HandlerName
        detail.Material.AuditTime = auditRecord.CreatedAt
        _, actionName := e.inferActionFromStateChange(auditRecord.BeforeState, auditRecord.AfterState)
        detail.Material.AuditAction = actionName
        
        // 从 extend 中获取物料相关的表单字段
        if extendData != nil {
            // 可以从 extend 中获取物料相关字段，比如：
            // if materialList, exists := extendData["material_list"]; exists { ... }
            // if totalAmount, exists := extendData["total_amount"]; exists { ... }
        }
    }
}
```

#### 核销阶段 (状态 ±400)
```go
// 4. 核销数据 - 使用绝对值判断状态
if abs(apply.State) >= 400 {
    var auditRecord *api.StatusRecordWithUser
    var extendData map[string]interface{}
    
    if record, exists := stateAuditMap[400]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[400]
    } else if record, exists := stateAuditMap[-400]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[-400]
    }
    
    if auditRecord != nil {
        detail.WriteOff.AuditorName = auditRecord.HandlerName
        detail.WriteOff.AuditTime = auditRecord.CreatedAt
        _, actionName := e.inferActionFromStateChange(auditRecord.BeforeState, auditRecord.AfterState)
        detail.WriteOff.AuditAction = actionName
        
        // 从 extend 中获取核销相关的表单字段
        if extendData != nil {
            if writeOffAdvice, exists := extendData["write_off_advice"]; exists {
                if adviceStr, ok := writeOffAdvice.(string); ok {
                    detail.WriteOff.WriteOffAdvice = adviceStr
                }
            }
            if pay, exists := extendData["pay"]; exists {
                if payStr, ok := pay.(string); ok {
                    detail.WriteOff.Pay = payStr
                }
            }
            if bagSupportAmount, exists := extendData["bag_support_amount"]; exists {
                if amount, ok := bagSupportAmount.(float64); ok {
                    detail.WriteOff.BagSupportAmount = amount
                }
            }
        }
    }
}
```

#### 渠道审核阶段 (状态 ±500)
```go
// 5. 渠道审核数据 - 使用绝对值判断状态
if abs(apply.State) >= 500 {
    var auditRecord *api.StatusRecordWithUser
    var extendData map[string]interface{}
    
    if record, exists := stateAuditMap[500]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[500]
    } else if record, exists := stateAuditMap[-500]; exists {
        auditRecord = record
        extendData = stateExtendDataMap[-500]
    }
    
    if auditRecord != nil {
        detail.ChannelAudit.AuditorName = auditRecord.HandlerName
        detail.ChannelAudit.AuditTime = auditRecord.CreatedAt
        _, actionName := e.inferActionFromStateChange(auditRecord.BeforeState, auditRecord.AfterState)
        detail.ChannelAudit.AuditAction = actionName
        
        // 从 extend 中获取渠道审核相关的表单字段
        if extendData != nil {
            if channelAdvice, exists := extendData["channel_advice"]; exists {
                if adviceStr, ok := channelAdvice.(string); ok {
                    detail.ChannelAudit.ChannelAdvice = adviceStr
                }
            }
            if channelPhotos, exists := extendData["channel_photos"]; exists {
                if photosSlice, ok := channelPhotos.([]interface{}); ok {
                    photos := make([]string, len(photosSlice))
                    for i, photo := range photosSlice {
                        if photoStr, ok := photo.(string); ok {
                            photos[i] = photoStr
                        }
                    }
                    detail.ChannelAudit.ChannelPhotos = photos
                }
            }
            if realOpenTime, exists := extendData["real_open_time"]; exists {
                if timeInt, ok := realOpenTime.(float64); ok {
                    detail.ChannelAudit.RealOpenTime = int64(timeInt)
                }
            }
        }
    }
}
```

## 📊 状态判断逻辑

### 绝对值判断的优势

1. **统一处理正负状态** - `abs(apply.State) >= 100` 可以同时处理状态 100 和 -100
2. **简化条件判断** - 不需要分别判断正负状态
3. **逻辑清晰** - 只要达到某个阶段的绝对值，就显示该阶段的数据

### 状态优先级

在获取审核记录时，优先获取正状态，如果没有则获取负状态：

```go
if record, exists := stateAuditMap[200]; exists {
    auditRecord = record
    extendData = stateExtendDataMap[200]
} else if record, exists := stateAuditMap[-200]; exists {
    auditRecord = record
    extendData = stateExtendDataMap[-200]
}
```

## 🎯 数据流程

### 1. 状态转换时存储
```json
// extend 字段只存储表单提交的数据
{
  "audit_advice": "申请材料齐全，审核通过",
  "audit_remark": "建议尽快安排物料支持",
  "priority": "high"
}
```

### 2. 获取详情时提取
```go
// 从 extend 中提取字段，直接赋值给结构体
if auditAdvice, exists := extendData["audit_advice"]; exists {
    if adviceStr, ok := auditAdvice.(string); ok {
        detail.Audit.AuditAdvice = adviceStr
    }
}
```

### 3. 返回数据结构
```json
{
  "audit": {
    "id": 123,
    "state": 100,
    "action": "audit",
    "audit_advice": "申请材料齐全，审核通过",  // 从 extend 中获取
    "extend": {
      "auditor_name": "张审核员",           // 审核人信息
      "audit_time": "2023-12-01 10:30:00", // 审核时间
      "audit_action": "审核通过"            // 审核操作
    }
  }
}
```

## ✅ 核心优势

1. **数据来源统一** - 所有表单数据都从 extend 字段获取
2. **状态判断简化** - 使用绝对值统一处理正负状态
3. **字段映射直接** - 直接将 extend 中的字段赋值给结构体字段
4. **逻辑清晰** - 每个阶段的数据获取逻辑独立且清晰
5. **扩展性强** - 可以轻松添加新的字段映射

## 🚀 使用示例

### 审核阶段提交数据
```json
// 状态转换时 extend 存储
{
  "audit_advice": "申请材料齐全，审核通过",
  "audit_remark": "建议尽快安排物料支持"
}

// 获取详情时返回
{
  "audit": {
    "audit_advice": "申请材料齐全，审核通过",
    "extend": {
      "auditor_name": "张审核员",
      "audit_time": "2023-12-01 10:30:00",
      "audit_action": "审核通过"
    }
  }
}
```

### 核销阶段提交数据
```json
// 状态转换时 extend 存储
{
  "write_off_advice": "核销材料完整",
  "pay": "12000.00",
  "bag_support_amount": 3000.00
}

// 获取详情时返回
{
  "writeoff": {
    "write_off_advice": "核销材料完整",
    "pay": "12000.00",
    "bag_support_amount": 3000.00,
    "auditor_name": "赵财务",
    "audit_time": "2023-12-10 16:45:00",
    "audit_action": "核销初审通过"
  }
}
```

这个实现完美满足了你的需求：extend 字段只存储表单提交的数据，使用绝对值判断状态，并直接将字段赋值给对应的结构体字段！🎉
