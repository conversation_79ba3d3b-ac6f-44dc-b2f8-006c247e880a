package validators

import (
	"fmt"

	"marketing/internal/model"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// PolicyValidator 政策验证器
type PolicyValidator struct{}

// NewPolicyValidator 创建政策验证器
func NewPolicyValidator() *PolicyValidator {
	return &PolicyValidator{}
}

// Validate 验证政策相关规则
func (v *PolicyValidator) Validate(ctx *gin.Context, req *statemachine.StateTransitionRequest) error {
	// 获取申请信息
	application, exists := req.Data["application"]
	if !exists {
		return fmt.Errorf("缺少申请信息")
	}
	
	// 获取政策信息
	policy, exists := req.Data["policy"]
	if !exists {
		return fmt.Errorf("缺少政策信息")
	}
	
	// 类型断言
	applyModel, ok := application.(*model.EndpointApplication)
	if !ok {
		return fmt.Errorf("申请信息格式错误")
	}
	
	policyModel, ok := policy.(*model.EndpointPolicy)
	if !ok {
		return fmt.Errorf("政策信息格式错误")
	}
	
	// 验证政策是否有效
	if policyModel.Enabled != 1 {
		return fmt.Errorf("政策已失效")
	}
	
	// 根据操作类型进行特定验证
	switch req.Action {
	case "submit_material":
		// 验证政策是否支持物料支持
		if policyModel.MaterialSupport != 1 {
			return fmt.Errorf("当前政策不支持物料支持")
		}
		
	case "submit_writeoff":
		// 验证政策是否支持金额支持
		if policyModel.AmountSupport <= 0 {
			return fmt.Errorf("当前政策不支持金额支持")
		}
		
		// 验证核销金额是否超出政策限制
		if amount, exists := req.Data["writeoff_amount"]; exists {
			if amountVal, ok := amount.(float64); ok {
				if amountVal > float64(policyModel.AmountSupport) {
					return fmt.Errorf("核销金额超出政策支持上限")
				}
			}
		}
	}
	
	_ = applyModel // 避免未使用变量警告
	
	return nil
}

// GetName 获取验证器名称
func (v *PolicyValidator) GetName() string {
	return "policy"
}
