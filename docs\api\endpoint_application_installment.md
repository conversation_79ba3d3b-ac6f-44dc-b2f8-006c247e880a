# 终端申请分期入账 API 文档

## 概述

终端申请分期入账管理接口，包括入账操作、分期列表查询等功能。**已简化移除核销审核功能，只保留入账操作。**

## 接口列表

### 1. 入账操作接口

**URL:** `PUT /admin/endpoint-application/installments/{id}/record`

**请求方式:** `PUT`

**权限要求:** 管理员权限

#### 请求参数

**路径参数:**
| 参数名 | 必选 | 类型 | 说明 |
|:-------|:-----|:-----|:-----|
| id | 是 | uint | 分期ID |

**Body参数:**
| 参数名 | 必选 | 类型 | 说明 |
|:-------|:-----|:-----|:-----|
| support_status | 是 | uint8 | 入账状态：1-已入账，2-中止 |
| record_remark | 否 | string | 入账备注 |

#### 请求示例

```bash
curl -X PUT "/admin/endpoint-application/installments/123/record" \
  -H "Content-Type: application/json" \
  -d '{
    "support_status": 1,
    "record_remark": "已完成入账操作"
  }'
```

#### 响应结果

**成功响应:**
```json
{
  "ok": 1,
  "msg": "ok"
}
```

**错误响应:**
```json
{
  "ok": 0,
  "msg": "该分期记录已入账"
}
```

---

### 2. 分期列表查询接口

**URL:** `GET /admin/endpoint-application/installments`

**请求方式:** `GET`

**权限要求:** 管理员权限

#### 请求参数

**Query参数:**
| 参数名 | 必选 | 类型 | 说明 |
|:-------|:-----|:-----|:-----|
| page | 否 | int | 页码，默认为1 |
| page_size | 否 | int | 每页数量，默认为20 |
| application_id | 否 | uint | 申请ID |
| year | 否 | uint16 | 年份 |
| month | 否 | uint8 | 月份 |
| support_status | 否 | uint8 | 入账状态：0-未入账，1-已入账，2-中止 |

#### 请求示例

```bash
# 基础查询
curl -X GET "/admin/endpoint-application/installments?page=1&page_size=10"

# 按条件筛选
curl -X GET "/admin/endpoint-application/installments?application_id=123&support_status=0"
```

#### 响应结果

**成功响应:**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": [
    {
      "id": 1,
      "application_id": 123,
      "application_name": "测试终端申请",
      "year": 2024,
      "month": 3,
      "date": "2024-03-01T00:00:00Z",
      "fee": 50000,
      "staff_fee": 10000,
      "mission_score": 85,
      "rent_proof": "https://example.com/rent_proof.jpg",
      "rent_proof_audit_state": 1,
      "rent_proof_audit_remark": "凭证审核通过",
      "rent_proof_updated_at": "2024-03-01T10:00:00Z",
      "support_status": 1,
      "staff_support_status": 1,
      "staff_record_remark": "人员支持已入账",
      "recorded_by": 1001,
      "recorded_at": "2024-03-15T14:30:00Z",
      "record_remark": "已完成入账操作",
      "created_at": "2024-03-01T00:00:00Z",
      "updated_at": "2024-03-15T14:30:00Z",
      "support_status_name": "已入账",
      "recorded_by_name": "管理员"
    }
  ],
  "total": 1
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|:-------|:-----|:-----|
| id | uint | 分期ID |
| application_id | uint | 申请ID |
| application_name | string | 申请名称 |
| year | uint16 | 年份 |
| month | uint8 | 月份 |
| date | time.Time | 分期日期 |
| fee | uint | 支持金额（分） |
| staff_fee | uint | 人员支持金额（分） |
| mission_score | uint | 任务考核分数 |
| rent_proof | string | 租金凭证图 |
| rent_proof_audit_state | uint8 | 租金凭证审核状态 |
| support_status | uint8 | 入账状态 |
| recorded_by | uint | 入账人ID |
| recorded_at | time.Time | 入账时间 |
| support_status_name | string | 入账状态名称 |

---

### 3. 分期详情查询接口

**URL:** `GET /admin/endpoint-application/installments/{id}`

**请求方式:** `GET`

**权限要求:** 管理员权限

#### 请求参数

**路径参数:**
| 参数名 | 必选 | 类型 | 说明 |
|:-------|:-----|:-----|:-----|
| id | 是 | uint | 分期ID |

#### 请求示例

```bash
curl -X GET "/admin/endpoint-application/installments/123"
```

#### 响应结果

**成功响应:**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "id": 123,
    "application_id": 456,
    "application_name": "测试终端申请",
    "year": 2024,
    "month": 3,
    "date": "2024-03-01T00:00:00Z",
    "fee": 50000,
    "staff_fee": 10000,
    "mission_score": 85,
    "rent_proof": "https://example.com/rent_proof.jpg",
    "rent_proof_audit_state": 1,
    "rent_proof_audit_remark": "凭证审核通过",
    "rent_proof_updated_at": "2024-03-01T10:00:00Z",
    "support_status": 1,
    "staff_support_status": 1,
    "staff_record_remark": "人员支持已入账",
    "recorded_by": 1001,
    "recorded_at": "2024-03-15T14:30:00Z",
    "record_remark": "已完成入账操作",
    "created_at": "2024-03-01T00:00:00Z",
    "updated_at": "2024-03-15T14:30:00Z",
    "support_status_name": "已入账",
    "recorded_by_name": "管理员"
  }
}
```

## 状态说明

### 入账状态 (support_status)
- `0` - 未入账
- `1` - 已入账
- `2` - 中止


### 租金凭证审核状态 (rent_proof_audit_state)
- `0` - 未审核
- `1` - 通过
- `2` - 不通过

## 业务流程

```mermaid
graph TD
    A[创建分期记录] --> B[等待入账操作]
    B --> C[管理员执行入账]
    C --> D{入账状态}
    D -->|已入账| E[发送入账通知]
    D -->|中止| F[终止流程]
    E --> G[流程完成]
    F --> G
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|:-------|:---------|:-----|
| 400 | 参数错误 | 请求参数格式不正确 |
| 404 | 分期记录不存在 | 指定的分期ID不存在 |
| 422 | 申请记录不存在 | 关联的申请记录不存在 |
| 422 | 渠道未审核或者审核不通过不允许入账 | 城市综合体堡垒店需要先审核 |
| 500 | 服务器内部错误 | 系统异常 |

## 注意事项

1. **简化流程**: 已移除核销审核功能，直接进行入账操作
2. **权限控制**: 所有接口都需要管理员权限
3. **特殊规则**: 城市综合体堡垒店（type=7）需要先审核才能入账
4. **入账通知**: 入账成功后会自动发送通知给相关用户
5. **金额单位**: 所有金额字段以"分"为单位
6. **分页限制**: 列表查询默认每页20条，最大100条
7. **数据完整性**: 删除或修改分期记录需要考虑关联的申请状态
8. **状态管理**: 支持入账（1）和中止（2）两种状态

## 相关接口

- `GET /admin/endpoint-application` - 获取终端申请列表
- `GET /admin/endpoint-application/{id}` - 获取申请详情
- `PUT /admin/endpoint-application/{id}/audit` - 审核申请

## 业务场景示例

### 1. 正常入账流程
```bash
# 1. 查询待入账的分期记录
curl -X GET "/admin/endpoint-application/installments?support_status=0"

# 2. 对指定分期进行入账操作
curl -X PUT "/admin/endpoint-application/installments/123/record" \
  -H "Content-Type: application/json" \
  -d '{
    "support_status": 1,
    "record_remark": "3月份支持费用入账"
  }'
```

### 2. 中止入账流程
```bash
# 对指定分期进行中止操作
curl -X PUT "/admin/endpoint-application/installments/123/record" \
  -H "Content-Type: application/json" \
  -d '{
    "support_status": 2,
    "record_remark": "因政策调整中止该期支持"
  }'
```

### 3. 入账通知功能
- 入账成功后，系统会自动计算该分期是第几期
- 向相关用户发送推送通知
- 通知格式：`您的店铺【店铺名称】，YYYY年MM月第N期支持费用已成功入账，请查收！`
