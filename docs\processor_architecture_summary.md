# 处理器驱动架构重构总结

## 🎯 重构目标

将原有的手动业务处理逻辑重构为处理器驱动的架构，实现：
- **配置驱动业务逻辑**：通过配置决定执行哪些处理器
- **状态机自动调用**：状态机根据配置自动调用相应的处理器
- **职责分离**：不同的业务逻辑分离到不同的处理器中
- **易于扩展**：新增业务逻辑只需要添加新的处理器

## 📁 新的文件结构

```
internal/service/endpoint_application/
├── apply.go                    # 主服务文件，包含状态机集成
├── processors/                 # 处理器目录
│   ├── state.go               # 通用状态更新处理器
│   ├── audit.go               # 审核处理器
│   ├── material.go            # 物料处理器
│   ├── postback.go            # 回传处理器
│   └── writeoff.go            # 核销处理器
└── validators/                 # 验证器目录
    ├── permission.go          # 权限验证器
    ├── data.go                # 数据验证器
    └── policy.go              # 政策验证器
```

## 🔧 核心组件

### 1. 处理器 (Processors)

#### StateProcessor - 通用状态处理器
```go
// 负责基本的状态更新和数据库操作
func (p *StateProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    // 设置基本状态信息
    updateData["state"] = int(req.TargetState)
    updateData["updated_at"] = time.Now()
    
    // 更新数据库
    return txRepo.UpdateEndpointApply(ctx, int(req.ApplicationID), updateData)
}
```

#### AuditProcessor - 审核处理器
```go
// 处理审核通过/拒绝的业务逻辑
func (p *AuditProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    switch req.Action {
    case "approve":
        updateData["audit_time"] = time.Now()
        updateData["audit_status"] = 1
        // 创建状态记录
        return p.createStatusRecord(ctx, txRepo, req, int(consts.ApplicationApproved))
    case "reject":
        // 处理拒绝逻辑
    }
}
```

#### MaterialProcessor - 物料处理器
```go
// 处理物料申请的业务逻辑
func (p *MaterialProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    switch req.Action {
    case "submit_material":
        // 验证申请状态
        // 保存物料数据
        // 计算总金额
    }
}
```

#### PostbackProcessor - 回传处理器
```go
// 处理建店回传的业务逻辑
func (p *PostbackProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    switch req.Action {
    case "complete_store":
        // 验证申请状态
        // 保存回传数据到专门的表
        // 处理各种文件和数据
    }
}
```

#### WriteOffProcessor - 核销处理器
```go
// 处理核销申请和渠道确认的业务逻辑
func (p *WriteOffProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    switch req.Action {
    case "submit_writeoff":
        // 处理核销初审
    case "channel_confirmation":
        // 处理渠道确认
    }
}
```

### 2. 验证器 (Validators)

#### PermissionValidator - 权限验证器
```go
// 验证用户是否有权限执行特定操作
func (v *PermissionValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
    switch req.Action {
    case "approve", "reject":
        // 审核操作需要管理员权限
    case "complete_store":
        // 建店回传可以由代理商执行
    }
}
```

#### DataValidator - 数据验证器
```go
// 验证请求数据的完整性和有效性
func (v *DataValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
    switch req.Action {
    case "reject":
        // 拒绝操作必须有备注
    case "complete_store":
        // 建店回传必须有必要的文件
    }
}
```

#### PolicyValidator - 政策验证器
```go
// 验证操作是否符合政策要求
func (v *PolicyValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
    // 验证政策是否有效
    // 验证操作是否符合政策规则
}
```

## 🔄 工作流程

### 1. 状态转换请求
```go
POST /agency/endpoint-applications/{id}/transition
{
  "action": "approve",
  "data": {
    "remark": "审核通过"
  }
}
```

### 2. Service层处理
```go
// 核心的状态转换方法
func (e endpointApplyService) TransitionState(c *gin.Context, req *api.StateTransitionReq) error {
    // 获取申请和政策信息
    apply, policy := ...

    // 创建状态机
    stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)

    // 构建状态转换请求
    stateReq := &statemachine.StateTransitionRequest{
        ApplicationID: req.ID,
        CurrentState:  consts.EndpointApplicationState(apply.State),
        Action:        req.Action,
        Data:          req.Data,
    }

    // 添加事务DAO和Service到数据中
    stateReq.Data["txRepo"] = txRepo
    stateReq.Data["service"] = e

    // 执行状态机转换（自动调用配置的处理器）
    return stateMachine.ExecuteTransition(c, stateReq)
}

// 原有的业务方法都通过状态机处理
func (e endpointApplyService) MaterialSupport(c *gin.Context, id uint, req []*api.EndpointMaterialSupport) error {
    return e.TransitionState(c, &api.StateTransitionReq{
        ID:     id,
        Action: "submit_material",
        Data:   map[string]interface{}{"material_list": req},
    })
}

func (e endpointApplyService) PostbackEndpointApply(c *gin.Context, req *api.PostbackEndpointApplyReq) error {
    return e.TransitionState(c, &api.StateTransitionReq{
        ID:     req.ID,
        Action: "complete_store",
        Data:   map[string]interface{}{...}, // 回传数据
    })
}

func (e endpointApplyService) WriteOff(c *gin.Context, req *api.WriteOffReq) error {
    return e.TransitionState(c, &api.StateTransitionReq{
        ID:     req.ID,
        Action: "submit_writeoff",
        Data:   map[string]interface{}{...}, // 核销数据
    })
}
```

### 3. 状态机自动执行
```go
// 状态机内部逻辑
func (sm *EndpointApplicationStateMachine) executeStateChange(ctx *gin.Context, node *StateNode, req *StateTransitionRequest) error {
    updateData := map[string]any{}

    // 执行配置的处理器
    for _, processorName := range node.Processors {
        if processor, exists := sm.processors[processorName]; exists {
            if err := processor.Process(ctx, req, updateData); err != nil {
                return err
            }
        }
    }

    return nil
}
```

## 📋 配置示例

### 标准工作流配置
```json
{
  "name": "Standard Store Setup Workflow",
  "validators": ["permission", "data", "policy"],
  "processors": ["state", "audit", "postback", "material", "writeoff"],
  "states": {
    "0": {
      "id": 0,
      "name": "Pending Audit",
      "processors": ["state", "audit"],
      "actions": [
        {"type": "approve", "label": "Approve", "config": {"next_state": 100}},
        {"type": "reject", "label": "Reject", "config": {"next_state": -1}}
      ]
    },
    "100": {
      "id": 100,
      "name": "Approved",
      "processors": ["state", "material"],
      "actions": [
        {"type": "submit_material", "label": "Submit Material Request", "config": {"next_state": 200}}
      ]
    }
  }
}
```

## ✅ 重构成果

### 1. 代码结构清晰
- ✅ 处理器和验证器分离到独立文件夹
- ✅ 每个处理器职责单一，易于维护
- ✅ Service层代码大幅简化

### 2. 配置驱动
- ✅ 通过配置决定执行哪些处理器
- ✅ 不同状态可以配置不同的处理器组合
- ✅ 修改业务逻辑只需要调整配置

### 3. 易于扩展
- ✅ 添加新处理器只需要实现Processor接口
- ✅ 在GetProcessor方法中注册新处理器
- ✅ 在配置中指定使用新处理器

### 4. 业务逻辑完整
- ✅ MaterialSupport方法的逻辑集成到MaterialProcessor
- ✅ PostbackEndpointApply方法的逻辑集成到PostbackProcessor  
- ✅ WriteOff方法的逻辑集成到WriteOffProcessor
- ✅ ChannelConfirmation方法的逻辑集成到WriteOffProcessor

## 🚀 使用示例

### 添加新的处理器
```go
// 1. 创建新处理器
type NotificationProcessor struct{}

func (p *NotificationProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
    // 发送通知逻辑
    return nil
}

// 2. 在GetProcessor中注册
case "notification":
    return processors.NewNotificationProcessor(), nil

// 3. 在配置中使用
"processors": ["state", "audit", "notification"]
```

### 自定义工作流
```go
// 不同的政策可以使用不同的工作流配置
// 状态机会根据政策ID自动加载相应的配置
stateMachine, err := statemachine.CreateStateMachineForPolicy(policyID, workflowDao, validatorProvider, processorProvider)
```

## 🎉 总结

新的处理器驱动架构完全实现了您的期望：
- **配置什么处理器就执行什么业务逻辑**
- **状态机根据配置自动调用处理器**
- **所有业务逻辑都在处理器中实现**
- **Service层只负责协调，不包含具体业务逻辑**

这样的设计既保持了代码的清晰性，又提供了极大的灵活性和可扩展性！
