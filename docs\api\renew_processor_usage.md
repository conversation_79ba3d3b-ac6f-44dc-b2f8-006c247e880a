# 老店改造工作流处理器使用说明

## 概述
`RenewProcessor` 是专门用于处理老店改造申请的工作流处理器。与新建店铺的 `ApprovedProcessor` 不同，它不创建新终端，而是更新现有终端的信息。

## 功能特性

### 1. 更新现有终端信息
- 根据申请信息更新关联终端的基本信息
- 包括：名称、电话、地址、经纬度、负责人等
- 使用事务确保数据一致性

### 2. 数据传递
- 更新完成后将终端信息传递给后续处理器
- 设置 `endpoint` 和 `endpoint_code` 供通知处理器使用

### 3. 数据验证
- 验证申请必须关联现有终端
- 确保关联的终端存在
- 提供详细的错误信息

## 使用场景

### 适用于以下申请类型：
- **老店改造** (`renew`): 现有终端的装修改造
- **终端升级** (`upgrade`): 终端形象升级
- 其他需要更新现有终端信息的场景

### 不适用场景：
- **新建店铺** (`new`): 应使用 `ApprovedProcessor`
- 没有关联终端的申请

## 处理器配置

### 1. 在工作流模板中配置
```json
{
  "state": "approved",
  "processors": [
    {
      "name": "renew",
      "config": {}
    },
    {
      "name": "notification", 
      "config": {}
    }
  ]
}
```

### 2. 处理器执行顺序
建议的处理器执行顺序：
1. `renew` - 更新终端信息
2. `notification` - 发送通知
3. 其他业务处理器（如物料、分期等）

## 数据流处理

### 输入数据要求
```go
// 申请信息必须包含
type EndpointApplication struct {
    AddToEndpointId uint    // 必须 > 0，关联的终端ID
    Name            string  // 终端名称
    Phone           *string // 联系电话
    Province        int     // 省份
    City            int     // 城市
    District        int     // 区县
    Address         string  // 详细地址
    Blng            string  // 百度经度
    Blat            string  // 百度纬度
    Lng             string  // 高德经度
    Lat             string  // 高德纬度
    Manager         string  // 负责人
    ApplyType       string  // 申请类型
}
```

### 输出数据
处理器会在 `req.Data` 中设置：
- `endpoint`: 更新后的终端信息
- `endpoint_code`: 终端编码（用于通知）

## 错误处理

### 常见错误及处理
1. **缺少事务DAO**: 返回错误，中断处理
2. **缺少申请信息**: 返回错误，中断处理
3. **申请信息格式错误**: 返回错误，中断处理
4. **未关联终端**: 返回 "老店改造申请必须关联现有终端"
5. **终端不存在**: 返回 "关联的终端不存在"
6. **更新失败**: 返回具体的数据库错误信息

## 实现细节

### 1. 终端信息更新
```go
// 更新的字段包括：
existingEndpoint.Name = apply.Name
existingEndpoint.Phone = apply.Phone
existingEndpoint.Province = apply.Province
existingEndpoint.City = apply.City
existingEndpoint.District = apply.District
existingEndpoint.Address = address  // 自动拼接完整地址
existingEndpoint.Blng = &apply.Blng
existingEndpoint.Blat = &apply.Blat
existingEndpoint.Lng = apply.Lng
existingEndpoint.Lat = apply.Lat
existingEndpoint.Manager = &apply.Manager
```

### 2. 地址处理
自动将省市区代码拼接到详细地址前：
```go
address := apply.Address
if apply.Province > 0 {
    provinceStr := strconv.Itoa(apply.Province)
    cityStr := strconv.Itoa(apply.City)
    districtStr := strconv.Itoa(apply.District)
    address = provinceStr + cityStr + districtStr + apply.Address
}
```

## 与其他处理器的对比

| 特性 | RenewProcessor | ApprovedProcessor |
|------|----------------|------------------|
| 主要功能 | 更新现有终端 | 创建新终端 |
| 终端ID来源 | apply.AddToEndpointId | 新生成 |
| 地址处理 | 拼接完整地址 | 拼接完整地址 |
| 通知机制 | 数据传递给通知处理器 | 数据传递给通知处理器 |
| 事务支持 | ✅ | ✅ |
| 错误处理 | 详细错误信息 | 详细错误信息 |

## 使用示例

### 工作流模板配置
```yaml
name: "老店改造审批流程"
states:
  - id: 1
    name: "待审核"
    type: "pending"
  - id: 2
    name: "审核通过"
    type: "approved"
    processors:
      - name: "renew"
      - name: "notification"
      - name: "material"

transitions:
  - from: 1
    to: 2
    action: "approve"
    validators:
      - name: "permission"
      - name: "data"
```

### 调用示例
```go
// 在状态机中自动调用，无需手动调用
// 处理器会在状态转换时自动执行

// 状态转换请求
req := &statemachine.StateTransitionRequest{
    ApplicationID: 123,
    FromState:     1,
    ToState:       2,
    Action:        "approve",
    Data: map[string]any{
        "application": applyModel,
        "service":     serviceInstance,
        "txRepo":      transactionRepo,
    },
}
```

## 注意事项

1. **必须关联终端**: 申请的 `AddToEndpointId` 必须大于 0
2. **事务安全**: 所有数据库操作都在事务中进行
3. **职责单一**: 只负责更新终端信息，通知由专门的通知处理器处理
4. **地址格式**: 会自动拼接省市区代码到地址前
5. **数据传递**: 将更新后的终端信息传递给后续处理器

## 扩展说明

如果需要扩展功能，可以：
1. 在 `updateEndpoint` 方法中添加更多字段更新
2. 添加额外的数据验证
3. 集成其他业务系统的回调

该处理器设计遵循单一职责原则，专注于终端信息更新，与通知处理器等其他处理器协同工作，确保老店改造流程的完整性和可靠性。
