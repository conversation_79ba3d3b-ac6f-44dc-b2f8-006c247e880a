package operation

import (
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/db"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type OpArticleCategoryDao interface {
	CreateArticleCategory(c *gin.Context, category *model.OpArticleCategory) error
	UpdateArticleCategory(c *gin.Context, id int, uMap map[string]interface{}) error
	DeleteArticleCategory(c *gin.Context, id int) error
	GetArticleCategoryList(c *gin.Context) ([]operation.ArticleCategoryListRspItem, error)
	GetArticleCategoryById(c *gin.Context, id int) *operation.ArticleCategoryListRspItem
	GetDescendantAndSelfIds(c *gin.Context, id uint) ([]uint, error)
}

// OpArticleCategoryDaoImpl 实现 OpArticleCategoryDao 接口
type OpArticleCategoryDaoImpl struct {
	db *gorm.DB
}

// NewOpArticleCategoryDao 创建 OpArticleCategoryDao 实例
func NewOpArticleCategoryDao() OpArticleCategoryDao {
	return &OpArticleCategoryDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpArticleCategoryDaoImpl) CreateArticleCategory(c *gin.Context, category *model.OpArticleCategory) error {
	return d.db.WithContext(c).Create(category).Error
}

func (d *OpArticleCategoryDaoImpl) DeleteArticleCategory(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.OpArticleCategory{}, "id = ?", id).Error
}

func (d *OpArticleCategoryDaoImpl) UpdateArticleCategory(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.OpArticleCategory{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *OpArticleCategoryDaoImpl) GetArticleCategoryList(c *gin.Context) ([]operation.ArticleCategoryListRspItem, error) {
	var data []struct {
		model.OpArticleCategory
		Num uint `gorm:"column:num"`
	}

	err := d.db.WithContext(c).Model(&model.OpArticleCategory{}).
		Joins("left join op_article on op_article_category.id = op_article.category_id and op_article.deleted_at is null").
		Select("op_article_category.id,parent_id,op_article_category.name,ancestor,op_article_category.enabled,`order`,COUNT(op_article.id) as num").
		Group("op_article_category.id").
		Order("`order` ASC, id ASC").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	var categories []operation.ArticleCategoryListRspItem
	for _, v := range data {
		categories = append(categories, operation.ArticleCategoryListRspItem{
			ID:          v.ID,
			ParentID:    v.ParentID,
			Name:        v.Name,
			Enabled:     uint8(v.Enabled),
			Order:       int(v.Order),
			NumArticles: v.Num,
		})
	}

	return categories, err
}

func (d *OpArticleCategoryDaoImpl) GetArticleCategoryById(c *gin.Context, id int) *operation.ArticleCategoryListRspItem {
	var data struct {
		model.OpArticleCategory
		Num uint `gorm:"column:num"`
	}

	err := d.db.WithContext(c).Model(&model.OpArticleCategory{}).
		Joins("left join op_article on op_article_category.id = op_article.category_id and op_article.deleted_at is null").
		Select("op_article_category.id,parent_id,op_article_category.name,ancestor,op_article_category.enabled,`order`,COUNT(op_article.id) as num").
		Where("op_article_category.id = ?", id).
		Group("op_article_category.id").
		First(&data).Error
	if err != nil {
		return nil
	}

	return &operation.ArticleCategoryListRspItem{
		ID:          data.ID,
		ParentID:    data.ParentID,
		Name:        data.Name,
		Enabled:     uint8(data.Enabled),
		Order:       int(data.Order),
		NumArticles: data.Num,
	}
}

// GetDescendantAndSelfIds 获取分类及其所有子分类的ID
func (d *OpArticleCategoryDaoImpl) GetDescendantAndSelfIds(c *gin.Context, id uint) ([]uint, error) {
	var descendantIds []uint

	// 使用FIND_IN_SET函数查找所有包含指定ID的ancestor字段
	err := d.db.WithContext(c).Model(&model.OpArticleCategory{}).
		Where("FIND_IN_SET(?, ancestor)", id).
		Pluck("id", &descendantIds).Error
	if err != nil {
		return nil, err
	}

	// 添加自身ID
	descendantIds = append(descendantIds, id)

	return descendantIds, nil
}
