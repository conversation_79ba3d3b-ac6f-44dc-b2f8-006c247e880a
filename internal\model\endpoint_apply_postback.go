package model

import (
	"marketing/internal/pkg/types"
	"time"
)

// EndpointApplicationPostback represents the endpoint_application_postback table in the database.
type EndpointApplicationPostback struct {
	ID                 uint64        `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	ApplicationID      int           `gorm:"not null;index:idx_application_id;comment:申请ID"`
	WriteOffTable      string        `gorm:"size:255;default:null;comment:批复表文件路径"`
	LeaseContract      string        `gorm:"size:255;default:null;comment:租赁合同文件路径"`
	AnnualRent         float64       `gorm:"type:decimal(15,2);comment:年租金金额"`
	DesignRenderings   string        `gorm:"type:text;default:null;comment:设计效果图路径数组"`
	RenovationPhotos   string        `gorm:"type:text;default:null;comment:装修实景图路径数组"`
	RenovationVideos   string        `gorm:"type:text;default:null;comment:装修实景视频路径数组"`
	Diploma            string        `gorm:"size:255;comment:店长毕业证书路径"`
	EndpointGroupPhoto string        `gorm:"size:255;comment:店长与门店合影路径"`
	ConfirmDate        time.Time     `gorm:"default:null;comment:核销回传日期"` // 使用指针以允许 NULL 值
	Extend             types.JSONMap `gorm:"type:text;comment:扩展信息,后续增加的扩展字段,json格式"`
	CreatedAt          time.Time     `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt          time.Time     `gorm:"autoUpdateTime;comment:更新时间"`
}

func (EndpointApplicationPostback) TableName() string {
	return "endpoint_application_postback"
}
