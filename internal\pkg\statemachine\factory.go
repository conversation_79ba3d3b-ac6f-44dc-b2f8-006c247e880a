package statemachine

import (
	"encoding/json"
	"fmt"
)

// createStateMachineForPolicy 根据政策创建状态机
// 这是一个无状态函数，线程安全
func createStateMachineForPolicy(policyID int, workflowDao WorkflowConfigDao, validatorProvider ValidatorProvider, processorProvider ProcessorProvider) (StateMachine, error) {
	sm := &EndpointApplicationStateMachine{
		validators: make(map[string]Validator),
		processors: make(map[string]Processor),
	}

	// 根据政策ID获取工作流配置
	workflowConfig, err := loadWorkflowForPolicy(policyID, workflowDao)
	if err != nil {
		return nil, err
	}

	// 从配置中获取并注册验证器
	if err := loadValidatorsFromConfig(sm, workflowConfig, validatorProvider); err != nil {
		return nil, err
	}

	// 从配置中获取并注册处理器
	if err := loadProcessorsFromConfig(sm, workflowConfig, processorProvider); err != nil {
		return nil, err
	}

	// 加载工作流
	if err := sm.LoadWorkflow(workflowConfig); err != nil {
		return nil, err
	}

	return sm, nil
}

// loadWorkflowForPolicy 根据政策加载工作流配置
func loadWorkflowForPolicy(policyID int, workflowDao WorkflowConfigDao) (*WorkflowConfig, error) {
	// 获取政策信息
	policy, err := workflowDao.GetPolicyByID(policyID)
	if err != nil {
		return nil, err
	}

	// 如果政策指定了工作流模板，使用指定的模板
	if policy.GetWorkflowTemplate() == 0 {
		return nil, fmt.Errorf("政策 %d 未指定工作流模板", policyID)
	}
	template, err := workflowDao.GetWorkflowTemplateByID(policy.GetWorkflowTemplate())
	if err != nil {
		return nil, err
	}
	return parseWorkflowConfig(template.GetConfig())

}

// parseWorkflowConfig 解析工作流配置JSON
func parseWorkflowConfig(configJSON string) (*WorkflowConfig, error) {
	var config WorkflowConfig
	if err := json.Unmarshal([]byte(configJSON), &config); err != nil {
		return nil, fmt.Errorf("解析工作流配置失败: %w", err)
	}
	return &config, nil
}

// loadValidatorsFromConfig 从配置中加载验证器
func loadValidatorsFromConfig(sm *EndpointApplicationStateMachine, config *WorkflowConfig, provider ValidatorProvider) error {
	validatorNames := config.Validators

	// 如果配置中没有指定验证器，使用默认验证器
	if len(validatorNames) == 0 {
		return nil
	}

	// 从提供者获取并注册验证器
	for _, name := range validatorNames {
		validator, err := provider.GetValidator(name)
		if err != nil {
			return fmt.Errorf("获取验证器 %s 失败: %w", name, err)
		}
		sm.RegisterValidator(validator)
	}

	return nil
}

// loadProcessorsFromConfig 从配置中加载处理器
func loadProcessorsFromConfig(sm *EndpointApplicationStateMachine, config *WorkflowConfig, provider ProcessorProvider) error {
	// 收集所有状态节点中的处理器名称（去重）
	processorNames := make(map[string]bool)

	// 遍历所有状态节点，收集处理器名称
	for stateKey, stateNode := range config.States {
		fmt.Printf("状态 %s 的处理器: %v\n", stateKey, stateNode.Processors)
		for _, processorName := range stateNode.Processors {
			processorNames[processorName] = true
		}
	}

	// 同时也收集全局处理器（如果有的话）
	for _, processorName := range config.Processors {
		processorNames[processorName] = true
	}

	// 如果没有找到任何处理器，返回
	if len(processorNames) == 0 {
		return nil
	}

	// 从提供者获取并注册处理器
	for name := range processorNames {
		processor, err := provider.GetProcessor(name)
		if err != nil {
			return fmt.Errorf("获取处理器 %s 失败: %w", name, err)
		}
		sm.RegisterProcessor(processor)
	}

	return nil
}

// CreateStateMachineForPolicy 根据政策创建状态机 (无状态函数，线程安全)
// 这是一个纯函数，不依赖任何全局状态，完全线程安全
func CreateStateMachineForPolicy(policyID int, workflowDao WorkflowConfigDao, validatorProvider ValidatorProvider, processorProvider ProcessorProvider) (StateMachine, error) {
	return createStateMachineForPolicy(policyID, workflowDao, validatorProvider, processorProvider)
}
