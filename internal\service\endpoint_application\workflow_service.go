package endpoint_application

import (
	"encoding/json"
	"marketing/internal/api/endpoint_application"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"
	"marketing/internal/pkg/types"

	"github.com/gin-gonic/gin"
)

// WorkflowService 工作流管理服务接口
type WorkflowService interface {
	// GetWorkflowTemplates 获取工作流模板列表
	GetWorkflowTemplates(c *gin.Context) ([]*endpoint_application.WorkflowTemplateResp, error)

	// CreateWorkflowTemplate 创建工作流模板
	CreateWorkflowTemplate(c *gin.Context, req *endpoint_application.CreateWorkflowTemplateReq) error

	// UpdateWorkflowTemplate 更新工作流模板
	UpdateWorkflowTemplate(c *gin.Context, id int, req *endpoint_application.UpdateWorkflowTemplateReq) error

	// DeleteWorkflowTemplate 删除工作流模板
	DeleteWorkflowTemplate(c *gin.Context, id int) error

	//GetWorkflowTemplate 获取工作流模板详情
	GetWorkflowTemplate(c *gin.Context, id int) (*endpoint_application.WorkflowTemplateDetailResp, error)
}

// workflowService 工作流管理服务实现
type workflowService struct {
	workflowDao applicationDao.WorkflowConfigDao
	policyDao   applicationDao.EndpointPolicyDao
}

// NewWorkflowService 创建工作流管理服务
func NewWorkflowService(
	workflowDao applicationDao.WorkflowConfigDao,
	policyDao applicationDao.EndpointPolicyDao,
) WorkflowService {
	return &workflowService{
		workflowDao: workflowDao,
		policyDao:   policyDao,
	}
}

// GetWorkflowTemplates 获取工作流模板列表
func (s *workflowService) GetWorkflowTemplates(c *gin.Context) ([]*endpoint_application.WorkflowTemplateResp, error) {
	templates, err := s.workflowDao.GetWorkflowTemplates(c)
	if err != nil {
		return nil, err
	}

	var resp []*endpoint_application.WorkflowTemplateResp
	for _, template := range templates {
		resp = append(resp, &endpoint_application.WorkflowTemplateResp{
			ID:          template.ID,
			Name:        template.Name,
			Slug:        template.Slug,
			Description: template.Description,
			Version:     template.Version,
			IsActive:    template.IsActive,
			CreatedAt:   types.CustomTime(template.CreatedAt),
			UpdatedAt:   types.CustomTime(template.UpdatedAt),
		})
	}

	return resp, nil
}

// CreateWorkflowTemplate 创建工作流模板
func (s *workflowService) CreateWorkflowTemplate(c *gin.Context, req *endpoint_application.CreateWorkflowTemplateReq) error {
	// 验证工作流配置
	if err := s.ValidateWorkflowConfig(c, req.Config); err != nil {
		return err
	}

	// 设置配置中的名称和标识符 保持内外一致
	req.Config.Name = req.Name
	req.Config.Slug = req.Slug

	// 序列化配置
	configJSON, err := json.Marshal(req.Config)
	if err != nil {
		return errors.NewErr("序列化工作流配置失败")
	}

	template := &model.WorkflowTemplate{
		Name:        req.Name,
		Slug:        req.Slug,
		Description: req.Description,
		Config:      string(configJSON),
		Version:     "1.0.0",
		IsActive:    1,
	}

	return s.workflowDao.CreateWorkflowTemplate(c, template)
}

// UpdateWorkflowTemplate 更新工作流模板
func (s *workflowService) UpdateWorkflowTemplate(c *gin.Context, id int, req *endpoint_application.UpdateWorkflowTemplateReq) error {
	// 验证工作流配置
	if req.Config != nil {
		if err := s.ValidateWorkflowConfig(c, req.Config); err != nil {
			return err
		}
	}

	template := &model.WorkflowTemplate{
		Name:        req.Name,
		Description: req.Description,
		Version:     req.Version,
	}

	// 如果有配置更新，序列化配置
	if req.Config != nil {
		// 设置配置中的名称和标识符 保持内外一致
		req.Config.Name = req.Name
		configJSON, err := json.Marshal(req.Config)
		if err != nil {
			return errors.NewErr("序列化工作流配置失败")
		}
		template.Config = string(configJSON)
	}

	return s.workflowDao.UpdateWorkflowTemplate(c, id, template)
}

// DeleteWorkflowTemplate 删除工作流模板
func (s *workflowService) DeleteWorkflowTemplate(c *gin.Context, id int) error {
	return s.workflowDao.DeleteWorkflowTemplate(c, id)
}

// GetWorkflowTemplate 获取工作流模板详情
func (s *workflowService) GetWorkflowTemplate(c *gin.Context, id int) (*endpoint_application.WorkflowTemplateDetailResp, error) {
	templates, err := s.workflowDao.GetWorkflowTemplates(c)
	if err != nil {
		return nil, err
	}

	for _, template := range templates {
		if template.ID == id {
			// 解析配置
			config, err := applicationDao.ParseWorkflowConfig(template.Config)
			if err != nil {
				return nil, errors.NewErr("解析工作流配置失败")
			}

			return &endpoint_application.WorkflowTemplateDetailResp{
				ID:          template.ID,
				Name:        template.Name,
				Description: template.Description,
				Version:     template.Version,
				IsActive:    template.IsActive,
				CreatedAt:   types.CustomTime(template.CreatedAt),
				UpdatedAt:   types.CustomTime(template.UpdatedAt),
				Config:      config,
			}, nil
		}
	}

	return nil, errors.NewErr("工作流模板不存在")
}

// ValidateWorkflowConfig 验证工作流配置
func (s *workflowService) ValidateWorkflowConfig(c *gin.Context, config *statemachine.WorkflowConfig) error {
	loader := statemachine.NewFileWorkflowLoader("")
	return loader.ValidateWorkflow(config)
}
