package processors

import (
	"time"

	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// RecordProcessor 入账确认处理器
type RecordProcessor struct{}

// NewRecordProcessor 创建入账确认处理器
func NewRecordProcessor() *RecordProcessor {
	return &RecordProcessor{}
}

// Process 处理入账确认逻辑
func (p *RecordProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}

	// 直接处理入账确认逻辑，不需要判断状态
	return p.handleRecorded(ctx, txRepo, req, updateData)
}

// handleRecorded 处理入账确认完成状态
func (p *RecordProcessor) handleRecorded(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取申请信息
	apply, exists := req.Data["application"]
	if !exists {
		return errors.NewErr("缺少申请信息")
	}

	applyModel, ok := apply.(*model.EndpointApplication)
	if !ok {
		return errors.NewErr("申请信息格式错误")
	}

	// 获取政策信息
	policy, exists := req.Data["policy"]
	if !exists {
		return errors.NewErr("缺少政策信息")
	}

	policyModel, ok := policy.(*model.EndpointPolicy)
	if !ok {
		return errors.NewErr("政策信息格式错误")
	}

	// 计算分期信息
	installments, err := p.computeInstallment(applyModel, policyModel)
	if err != nil {
		return err
	}

	// 如果有分期信息，保存分期数据
	if installments != nil {
		// 删除旧的分期信息
		if err := txRepo.DeleteEndpointApplicationInstallment(ctx, applyModel.ID); err != nil {
			return err
		}

		// 创建新的分期信息
		if err := txRepo.CreateEndpointApplicationInstallment(ctx, installments); err != nil {
			return err
		}
	}
	return nil
}

// computeInstallment 计算分期信息
func (p *RecordProcessor) computeInstallment(apply *model.EndpointApplication, policy *model.EndpointPolicy) ([]*model.EndpointApplicationInstallment, error) {
	// 如果没有政策或者缴费金额为0，不需要分期
	if apply.PolicyID == 0 || cast.ToFloat64(apply.Pay) == 0 {
		return nil, nil
	}

	// 如果政策没有设置分期，不需要分期
	if policy.Installments == 0 {
		return nil, nil
	}

	// 按月分期(如果当天日期大于等于20号，则从下一个月开始)
	now := time.Now()
	if now.Day() >= 20 {
		now = now.AddDate(0, 1, 0)
	}
	// 把日期固定到当月 1 号，避免溢出
	now = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	total := cast.ToFloat64(apply.Pay) * 100 // 转成分
	eachFee := total / float64(policy.Installments)
	lastFee := total - eachFee*(float64(policy.Installments)-1)

	var installments []*model.EndpointApplicationInstallment
	for i := 0; i < policy.Installments; i++ {
		if i > 0 {
			now = now.AddDate(0, 1, 0)
		}
		fee := uint(eachFee)
		if i == policy.Installments-1 {
			fee = uint(lastFee) // 最后一期修正差额
		}

		installments = append(installments, &model.EndpointApplicationInstallment{
			ApplicationID: uint(apply.ID),
			Year:          uint16(now.Year()),
			Month:         uint8(now.Month()),
			Date:          time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local),
			Fee:           fee,
		})
	}

	return installments, nil
}

// GetName 获取处理器名称
func (p *RecordProcessor) GetName() string {
	return "record"
}
