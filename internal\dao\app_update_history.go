package dao

import (
	"marketing/internal/api/notice"
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AppUpdateHistoryDao interface {
	// GetAppOptions 获取App选项列表
	GetAppOptions(c *gin.Context) ([]*notice.AppOptionsItemRes, error)
	// GetAppUpdateHistories 获取App更新历史列表和总数
	GetAppUpdateHistories(c *gin.Context, req *notice.AppUpdateHistoriesReq) ([]*notice.AppUpdateHistoryItem, int64, error)
}

type appUpdateHistoryDao struct {
	db *gorm.DB
}

func NewAppUpdateHistoryDao(db *gorm.DB) AppUpdateHistoryDao {
	return &appUpdateHistoryDao{
		db: db,
	}
}

// GetAppOptions 获取App选项列表
func (d *appUpdateHistoryDao) GetAppOptions(c *gin.Context) ([]*notice.AppOptionsItemRes, error) {
	var options []*notice.AppOptionsItemRes

	// 获取每个包名的最新记录
	subQuery := d.db.WithContext(c).Model(&model.AppUpdateHistory{}).
		Select("MAX(id) as id").
		Group("pkg_name")

	err := d.db.WithContext(c).Model(&model.AppUpdateHistory{}).
		Select("pkg_name as value, app_name as text").
		Where("id IN (?)", subQuery).
		Find(&options).Error

	return options, err
}

// GetAppUpdateHistories 获取App更新历史列表和总数
func (d *appUpdateHistoryDao) GetAppUpdateHistories(c *gin.Context, req *notice.AppUpdateHistoriesReq) ([]*notice.AppUpdateHistoryItem, int64, error) {
	var histories []*notice.AppUpdateHistoryItem
	var total int64

	// 先获取总数
	err := d.db.WithContext(c).Model(&model.AppUpdateHistory{}).
		Where("pkg_name = ?", req.PkgName).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页的历史记录
	query := d.db.WithContext(c).Model(&model.AppUpdateHistory{}).
		Select("app_name, version_name, update_content, devices, updated_at").
		Where("pkg_name = ?", req.PkgName).
		Order("updated_at DESC")

	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	err = query.Find(&histories).Error
	return histories, total, err
}
