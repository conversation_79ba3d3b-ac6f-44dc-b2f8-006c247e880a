package endpoint_application

import (
	"marketing/internal/consts"
	"marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// PolicyProcessor 政策处理器接口
type PolicyProcessor interface {
	ValidateApplication(ctx *gin.Context, apply *model.EndpointApplication, policy *model.EndpointPolicy) error
	ProcessStateTransition(ctx *gin.Context, req *statemachine.StateTransitionRequest) error
	GetRequiredFields(state consts.EndpointApplicationState) []string
	GetAvailableActions(state consts.EndpointApplicationState) []statemachine.Action
}

// PolicyProcessorFactory 政策处理器工厂
type PolicyProcessorFactory struct {
	processor    PolicyProcessor
	repo         endpoint_application.EndpointApplyDao
	stateMachine statemachine.StateMachine
}

// NewPolicyProcessorFactory 创建政策处理器工厂
func NewPolicyProcessorFactory(
	repo endpoint_application.EndpointApplyDao,
	stateMachine statemachine.StateMachine,
) *PolicyProcessorFactory {
	factory := &PolicyProcessorFactory{
		repo:         repo,
		stateMachine: stateMachine,
	}

	// 创建标准处理器
	factory.processor = &StandardPolicyProcessor{
		repo:         repo,
		stateMachine: stateMachine,
	}

	return factory
}

// GetProcessor 获取标准处理器
func (f *PolicyProcessorFactory) GetProcessor() PolicyProcessor {
	return f.processor
}

// GetProcessorByTemplate 根据模板获取处理器（兼容性方法）
func (f *PolicyProcessorFactory) GetProcessorByTemplate(template string) (PolicyProcessor, error) {
	// 不再区分模板类型，统一返回标准处理器
	return f.processor, nil
}

// StandardPolicyProcessor 标准政策处理器
type StandardPolicyProcessor struct {
	repo         endpoint_application.EndpointApplyDao
	stateMachine statemachine.StateMachine
}

func (p *StandardPolicyProcessor) ValidateApplication(ctx *gin.Context, apply *model.EndpointApplication, policy *model.EndpointPolicy) error {
	// 基础验证逻辑
	if apply.ID == 0 {
		return errors.NewErr("申请ID不能为空")
	}

	if policy.ID == 0 {
		return errors.NewErr("政策ID不能为空")
	}

	// 验证政策是否启用
	if policy.Enabled != 1 {
		return errors.NewErr("当前政策已禁用")
	}

	return nil
}

func (p *StandardPolicyProcessor) ProcessStateTransition(ctx *gin.Context, req *statemachine.StateTransitionRequest) error {
	return p.stateMachine.ExecuteTransition(ctx, req)
}

func (p *StandardPolicyProcessor) GetRequiredFields(state consts.EndpointApplicationState) []string {
	switch state {
	case consts.ApplicationWaitingReview:
		return []string{"pics", "business_license", "store_name", "store_address"}
	case consts.ApplicationApproved:
		return []string{"material_list"}
	case consts.ApplicationMaterialSupport:
		return []string{"support_attachment"}
	case consts.ApplicationPostback:
		return []string{"write_off_table", "lease_contract", "renovation_photos"}
	default:
		return []string{"pics"}
	}
}

func (p *StandardPolicyProcessor) GetAvailableActions(state consts.EndpointApplicationState) []statemachine.Action {
	// 直接从状态机获取可用操作
	actions, err := p.stateMachine.GetAvailableActions(state)
	if err != nil {
		// 如果状态机返回错误，返回空的操作列表
		return []statemachine.Action{}
	}
	return actions
}


