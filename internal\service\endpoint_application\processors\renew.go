package processors

import (
	"strconv"

	endpointDao "marketing/internal/dao/endpoint"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// RenewProcessor 老店改造处理器
type RenewProcessor struct{}

// NewRenewProcessor 创建老店改造处理器
func NewRenewProcessor() *RenewProcessor {
	return &RenewProcessor{}
}

// Process 处理老店改造逻辑 - 更新现有终端信息
func (p *RenewProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return errors.NewErr("缺少事务DAO")
	}

	// 获取申请信息
	apply, exists := req.Data["application"]
	if !exists {
		return errors.NewErr("缺少申请信息")
	}

	applyModel, ok := apply.(*model.EndpointApplication)
	if !ok {
		return errors.NewErr("申请信息格式错误")
	}

	// 验证是否有关联的终端ID
	if applyModel.AddToEndpointId == 0 {
		return errors.NewErr("老店改造申请必须关联现有终端")
	}

	// 更新终端信息
	endpointInfo, err := p.updateEndpoint(ctx, txRepo, applyModel)
	if err != nil {
		return err
	}

	if endpointInfo != nil {
		// 将终端信息保存到请求数据中，供后续处理器使用
		req.Data["endpoint"] = endpointInfo
		req.Data["endpoint_code"] = endpointInfo.Code
	}

	return nil
}

// updateEndpoint 更新现有终端信息
func (p *RenewProcessor) updateEndpoint(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, apply *model.EndpointApplication) (*model.Endpoint, error) {
	// 获取事务数据库连接
	txDB := txRepo.GetDB(ctx)
	txEndpointDao := endpointDao.NewEndpointDao(txDB)

	// 先查询现有终端信息
	existingEndpoint, err := txEndpointDao.GetEndpointByID(ctx, apply.AddToEndpointId)
	if err != nil {
		return nil, errors.NewErr("查询现有终端失败: " + err.Error())
	}
	if existingEndpoint == nil {
		return nil, errors.NewErr("关联的终端不存在")
	}

	// 构建完整地址
	address := apply.Address
	if apply.Province > 0 {
		provinceStr := strconv.Itoa(apply.Province)
		cityStr := strconv.Itoa(apply.City)
		districtStr := strconv.Itoa(apply.District)
		address = provinceStr + cityStr + districtStr + apply.Address
	}

	// 更新现有终端的字段
	existingEndpoint.Name = apply.Name
	existingEndpoint.Phone = apply.Phone
	existingEndpoint.Province = apply.Province
	existingEndpoint.City = apply.City
	existingEndpoint.District = apply.District
	existingEndpoint.Address = address
	existingEndpoint.Blng = &apply.Blng
	existingEndpoint.Blat = &apply.Blat
	existingEndpoint.Lng = apply.Lng
	existingEndpoint.Lat = apply.Lat
	existingEndpoint.Manager = &apply.Manager

	// 执行更新
	updatedEndpoint, err := txEndpointDao.UpdateEndpoint(ctx, existingEndpoint)
	if err != nil {
		return nil, errors.NewErr("更新终端信息失败: " + err.Error())
	}

	return updatedEndpoint, nil
}

// GetName 获取处理器名称
func (p *RenewProcessor) GetName() string {
	return "renew"
}
