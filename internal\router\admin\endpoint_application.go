package admin

import (
	"marketing/internal/dao"
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/app_system"
	"marketing/internal/dao/endpoint"
	endpointApplicationDao "marketing/internal/dao/endpoint_application"
	repo "marketing/internal/dao/endpoint_application"
	"marketing/internal/dao/warranty"
	endpointApplicationHandler "marketing/internal/handler/admin/endpoint_application"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/redis"
	"marketing/internal/service"
	"marketing/internal/service/endpoint_application"
	appService "marketing/internal/service/endpoint_application"
	"marketing/internal/service/system"

	"github.com/gin-gonic/gin"
)

// EndpointApplicationRouter 终端政策路由
type EndpointApplicationRouter struct{}

// NewEndpointApplicationRouter 创建终端政策路由实例
func NewEndpointApplicationRouter() *EndpointApplicationRouter {
	return &EndpointApplicationRouter{}
}

// Register 注册终端政策路由
func (e *EndpointApplicationRouter) Register(r *gin.RouterGroup) {
	// 终端政策管理模块
	policyRouter := r.Group("/endpoint-policy")
	{
		var Db = db.GetDB()
		// 初始化 DAO, Service, and Controller
		policyDao := endpointApplicationDao.NewEndpointPolicyDao(Db)
		workflowDao := endpointApplicationDao.NewWorkflowConfigDao(Db)
		policyService := endpoint_application.NewEndpointPolicyService(policyDao, workflowDao)
		policyController := endpointApplicationHandler.NewEndpointPolicyHandler(policyService)

		// 终端政策的增删改查方法
		policyRouter.POST("", policyController.CreateEndpointPolicy)                   // 创建终端政策
		policyRouter.GET("", policyController.GetEndpointPolicyList)                   // 获取终端政策列表
		policyRouter.GET("/:id", policyController.GetEndpointPolicyByID)               // 根据ID获取终端政策
		policyRouter.PUT("/:id", policyController.UpdateEndpointPolicy)                // 更新终端政策
		policyRouter.PUT("/:id/enabled", policyController.UpdateEndpointPolicyEnabled) // 更新终端政策
		policyRouter.DELETE("/:id", policyController.DeleteEndpointPolicy)             // 删除终端政策

		//终端申请管理
		endpointApplyDao := repo.NewEndpointApplyDao(Db)
		endpointDao := endpoint.NewEndpointDao(Db)
		endpointTypeRepo := dao.NewEndpointTypeRepository(Db)
		configRepo := dao.NewConfigRepository(Db)
		subjectRepo := dao.NewGormTrainSubjectDao(Db)
		materialRepo := dao.NewMaterialDao()
		endpointImageDao := endpoint.NewEndpointImageDao(Db)
		warrantyRepo := warranty.NewWarrantyDao(Db)
		workflowRepo := repo.NewWorkflowConfigDao(Db)

		endpointApplyService := appService.NewEndpointApplyService(endpointApplyDao, policyDao, endpointDao, endpointTypeRepo, configRepo, subjectRepo, materialRepo, endpointImageDao, warrantyRepo, workflowRepo)
		installmentDao := repo.NewInstallmentDao(Db)

		// 添加推送通知相关的依赖
		redisClient := redis.NewRedis()
		userDaoImpl := userDao.NewUserDao(Db)
		pushDao := dao.NewPushNotificationDao(Db)
		typeDao := dao.NewAppNotificationTypeDao(Db)
		roleDao := dao.NewAdminRoleDao(Db)
		appSystem := app_system.NewAppSystem(db.GetDB())
		appSystemCache := app_system.NewAppSystemCache(redisClient)
		appSvc := system.NewAppSystemSvc(appSystem, appSystemCache)
		pushSvc := service.NewPushNotificationSvc(pushDao, appSvc, userDaoImpl, typeDao, nil, endpointDao, roleDao, redisClient)

		installmentService := appService.NewInstallmentService(installmentDao, endpointApplyDao, userDaoImpl, pushSvc)
		applyHandler := endpointApplicationHandler.NewApplyHandler(endpointApplyService, installmentService)
		applyRouter := r.Group("/endpoint-application")
		{
			// RESTful 基础路由
			applyRouter.GET("", applyHandler.GetList) // 获取申请列表
			applyRouter.GET("/:id", applyHandler.GetEndpointApplyDetail)
			applyRouter.GET("/materials", applyHandler.GetMaterials) // 获取物料列表

			// 申请操作路由
			applyRouter.PUT("/:id/audit", applyHandler.AuditEndpointApply)
			applyRouter.POST("/:id/material-support", applyHandler.MaterialSupport)
			applyRouter.PUT("/:id/write-off", applyHandler.WriteOff)  //核销初审
			applyRouter.PUT("/:id/terminate", applyHandler.Terminate) //建店终止
			applyRouter.PUT("/:id/channel-confirmation", applyHandler.ChannelConfirmation)
			applyRouter.GET("/latest-endpoint-image", applyHandler.LatestEndpointImage)
			applyRouter.GET("/sales-amount", applyHandler.SalesAmount)

			// 状态机相关接口
			applyRouter.GET("/:id/available-actions", applyHandler.GetAvailableActions)
			applyRouter.GET("/:id/next-states", applyHandler.GetNextStates)
			applyRouter.POST("/:id/transition", applyHandler.TransitionState)

			// 入账相关接口
			applyRouter.PUT("/installments/:id", applyHandler.RecordAccount)            // 入账操作
			applyRouter.GET("/installments", applyHandler.GetInstallmentsByApplication) // 根据申请ID获取分期列表（不分页）
		}

		// 工作流模板管理
		workflowService := appService.NewWorkflowService(workflowDao, policyDao)
		workflowHandler := endpointApplicationHandler.NewWorkflowHandler(workflowService)
		workflowRouter := r.Group("/workflow-templates")
		{
			workflowRouter.GET("", workflowHandler.GetWorkflowTemplates)          // 获取工作流模板列表
			workflowRouter.POST("", workflowHandler.CreateWorkflowTemplate)       // 创建工作流模板
			workflowRouter.GET("/:id", workflowHandler.GetWorkflowTemplate)       // 获取单个工作流模板
			workflowRouter.PUT("/:id", workflowHandler.UpdateWorkflowTemplate)    // 更新工作流模板
			workflowRouter.DELETE("/:id", workflowHandler.DeleteWorkflowTemplate) // 删除工作流模板
		}
	}
}
