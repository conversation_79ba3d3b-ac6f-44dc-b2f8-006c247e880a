package statemachine

import (
	"fmt"
	"marketing/internal/consts"
	"time"

	"github.com/gin-gonic/gin"
)

// AuditProcessor 审核处理器
type AuditProcessor struct{}

func (p *AuditProcessor) GetName() string {
	return "audit_processor"
}

func (p *AuditProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 审核处理逻辑
	if req.Action == "approve" {
		updateData["audit_time"] = time.Now()
		updateData["audit_status"] = 1
		
		// 如果配置了创建终端，设置标记
		if action := p.findAction(req, "approve"); action != nil {
			if createEndpoint, exists := action.Config["create_endpoint"]; exists && createEndpoint == true {
				updateData["need_create_endpoint"] = 1
			}
		}
	} else if req.Action == "reject" {
		updateData["audit_time"] = time.Now()
		updateData["audit_status"] = -1
		
		if remark, exists := req.Data["remark"]; exists {
			updateData["audit_advice"] = remark
		}
	}
	
	return nil
}

func (p *AuditProcessor) findAction(req *StateTransitionRequest, actionType string) *Action {
	// 这里需要从状态机中获取当前节点的动作配置
	// 简化实现，实际应该从状态机配置中获取
	return &Action{
		Type:   actionType,
		Config: map[string]interface{}{"create_endpoint": true},
	}
}

// MaterialProcessor 物料处理器
type MaterialProcessor struct{}

func (p *MaterialProcessor) GetName() string {
	return "material_processor"
}

func (p *MaterialProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 物料处理逻辑
	if req.Action == "submit_material" {
		updateData["material_apply_time"] = time.Now()
		updateData["material_status"] = 1
		
		// 设置物料申请相关字段
		if materialList, exists := req.Data["material_list"]; exists {
			updateData["material_list"] = materialList
		}
		
		if needMaterial, exists := req.Data["need_material"]; exists {
			updateData["need_material"] = needMaterial
		}
	}
	
	return nil
}

// WriteOffProcessor 核销处理器
type WriteOffProcessor struct{}

func (p *WriteOffProcessor) GetName() string {
	return "writeoff_processor"
}

func (p *WriteOffProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 核销处理逻辑
	if req.Action == "writeoff_approve" {
		updateData["write_off_time"] = time.Now()
		updateData["write_off_status"] = 1
		
		// 设置核销金额
		if writeOffAmount, exists := req.Data["write_off_amount"]; exists {
			updateData["write_off_amount"] = writeOffAmount
		}
	} else if req.Action == "writeoff_reject" {
		updateData["write_off_time"] = time.Now()
		updateData["write_off_status"] = -1
		
		if remark, exists := req.Data["remark"]; exists {
			updateData["write_off_remark"] = remark
		}
	}
	
	return nil
}

// InstallmentProcessor 分期处理器
type InstallmentProcessor struct{}

func (p *InstallmentProcessor) GetName() string {
	return "installment_processor"
}

func (p *InstallmentProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 分期处理逻辑
	if req.Action == "record_confirmation" {
		if installments, exists := req.Data["policy.installments"]; exists {
			if periods, ok := installments.(int); ok && periods > 1 {
				// 创建分期记录
				updateData["installment_periods"] = periods
				updateData["installment_status"] = 1
				
				// 计算每期金额
				if totalAmount, exists := req.Data["total_amount"]; exists {
					if amount, ok := totalAmount.(float64); ok {
						updateData["installment_amount"] = amount / float64(periods)
					}
				}
			}
		}
	}
	
	return nil
}

// EndpointCreatorProcessor 终端创建处理器
type EndpointCreatorProcessor struct{}

func (p *EndpointCreatorProcessor) GetName() string {
	return "endpoint_creator"
}

func (p *EndpointCreatorProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 终端创建逻辑
	if req.TargetState == consts.ApplicationApproved {
		// 标记需要创建终端
		updateData["add_to_endpoint_id"] = 1
		updateData["endpoint_create_time"] = time.Now()
	}
	
	return nil
}

// NotificationProcessor 通知处理器
type NotificationProcessor struct{}

func (p *NotificationProcessor) GetName() string {
	return "notification_processor"
}

func (p *NotificationProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 通知处理逻辑
	notificationType := p.getNotificationType(req)
	if notificationType != "" {
		updateData["notification_type"] = notificationType
		updateData["notification_time"] = time.Now()
		
		// 这里可以集成消息队列发送通知
		// messageQueue.Send(notificationType, req.ApplicationID)
	}
	
	return nil
}

func (p *NotificationProcessor) getNotificationType(req *StateTransitionRequest) string {
	switch req.Action {
	case "approve":
		return "application_approved"
	case "reject":
		return "application_rejected"
	case "writeoff_approve":
		return "writeoff_approved"
	case "writeoff_reject":
		return "writeoff_rejected"
	default:
		return ""
	}
}

// CompletionProcessor 完成处理器
type CompletionProcessor struct{}

func (p *CompletionProcessor) GetName() string {
	return "completion_processor"
}

func (p *CompletionProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 完成处理逻辑
	if req.TargetState == consts.ApplicationCompleted {
		updateData["completed_time"] = time.Now()
		updateData["is_completed"] = 1
		
		// 设置完成状态的相关信息
		updateData["completion_remark"] = fmt.Sprintf("申请于 %s 完成处理", time.Now().Format("2006-01-02 15:04:05"))
	}
	
	return nil
}

// StateLogProcessor 状态日志处理器
type StateLogProcessor struct{}

func (p *StateLogProcessor) GetName() string {
	return "state_log_processor"
}

func (p *StateLogProcessor) Process(ctx *gin.Context, req *StateTransitionRequest, updateData map[string]any) error {
	// 状态日志记录逻辑
	logData := map[string]interface{}{
		"application_id": req.ApplicationID,
		"from_state":     int(req.CurrentState),
		"to_state":       int(req.TargetState),
		"action":         req.Action,
		"context":        req.Data,
		"created_at":     time.Now(),
	}
	
	// 获取操作人信息
	if userID, exists := ctx.Get("user_id"); exists {
		logData["actor_id"] = userID
	}
	
	if userType, exists := ctx.Get("user_type"); exists {
		logData["actor_type"] = userType
	}
	
	// 将日志数据添加到更新数据中，由上层服务处理实际的数据库插入
	updateData["state_log"] = logData
	
	return nil
}

// NewDefaultProcessors 创建默认处理器
func NewDefaultProcessors() []Processor {
	return []Processor{
		&AuditProcessor{},
		&MaterialProcessor{},
		&WriteOffProcessor{},
		&InstallmentProcessor{},
		&EndpointCreatorProcessor{},
		&NotificationProcessor{},
		&CompletionProcessor{},
		&StateLogProcessor{},
	}
}
