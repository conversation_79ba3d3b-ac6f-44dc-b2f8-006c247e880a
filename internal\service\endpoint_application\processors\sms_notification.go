package processors

import (
	"fmt"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/sms"
	"marketing/internal/pkg/statemachine"

	"github.com/gin-gonic/gin"
)

// SMSNotificationProcessor 建店短信通知处理器
type SMSNotificationProcessor struct{}

// NewSMSNotificationProcessor 创建建店短信通知处理器
func NewSMSNotificationProcessor() *SMSNotificationProcessor {
	return &SMSNotificationProcessor{}
}

// Process 处理建店短信通知逻辑
func (p *SMSNotificationProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取申请信息
	apply, exists := req.Data["application"]
	if !exists {
		return nil
	}

	applyModel, ok := apply.(*model.EndpointApplication)
	if !ok {
		return nil
	}

	// 获取终端代码
	endpointCode, exists := req.Data["endpoint_code"].(string)
	if !exists || endpointCode == "" {
		return nil
	}

	// 发送建店短信通知
	go func() {
		err := p.sendSMSNotification(applyModel, endpointCode)
		if err != nil {

		}
	}()
	return nil
}

// sendSMSNotification 发送建店短信通知
func (p *SMSNotificationProcessor) sendSMSNotification(apply *model.EndpointApplication, code string) error {
	signName := "读书郎"
	templateCode := "endpoint_code" // 短信模板代码
	templateParam := make(map[string]any)
	templateParam["name"] = apply.Name
	templateParam["code"] = code

	// 给申请人发送短信
	if apply.Phone != nil && *apply.Phone != "" {
		success, msg := sms.SendSMS(*apply.Phone, signName, templateCode, templateParam)
		if success {
			log.Info(fmt.Sprintf("建店短信通知发送成功: 手机号=%s, 终端代码=%s", *apply.Phone, code))
		} else {
			log.Error(fmt.Sprintf("建店短信通知发送失败: 手机号=%s, 终端代码=%s, 错误=%s", *apply.Phone, code, msg))
		}
	}

	// 给投资人发送短信
	if apply.Investor != "" {
		success, msg := sms.SendSMS(apply.Investor, signName, templateCode, templateParam)
		if success {
			log.Info(fmt.Sprintf("建店短信通知发送成功: 投资人手机号=%s, 终端代码=%s", apply.Investor, code))
		} else {
			log.Error(fmt.Sprintf("建店短信通知发送失败: 投资人手机号=%s, 终端代码=%s, 错误=%s", apply.Investor, code, msg))
		}
	}

	return nil
}

// GetName 获取处理器名称
func (p *SMSNotificationProcessor) GetName() string {
	return "sms_notification"
}
