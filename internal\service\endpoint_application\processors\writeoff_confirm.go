package processors

import (
	"encoding/json"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/statemachine"
	"time"

	"github.com/gin-gonic/gin"
)

// WriteOffConfirmProcessor 渠道核销确认处理器
type WriteOffConfirmProcessor struct{}

// NewWriteOffConfirmProcessor 创建渠道核销确认处理器
func NewWriteOffConfirmProcessor() *WriteOffConfirmProcessor {
	return &WriteOffConfirmProcessor{}
}

// Process 处理渠道核销确认逻辑
func (p *WriteOffConfirmProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}
	action := req.Action // 约定: "approve" | "reject"
	handlerMan := ctx.GetUint("uid")

	updateData["channel_audited_by"] = handlerMan
	updateData["channel_audited_at"] = time.Now()

	if action == "reject" {
		// 审核不通过：校验备注
		channelAdvice, exists := req.Data["channel_advice"]
		if !exists || channelAdvice == "" {
			return errors.NewErr("审核不通过，备注不能为空")
		}
		updateData["channel_advice"] = channelAdvice
		return nil
	}

	return p.handleConfirmation(ctx, txRepo, req, updateData)
}

// handleChannelConfirmation 处理渠道确认
func (p *WriteOffConfirmProcessor) handleConfirmation(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, req *statemachine.StateTransitionRequest, updateData map[string]any) error {

	// 先获取现有的申请信息
	ea, err := txRepo.GetEndpointApplyByID(ctx, int(req.ApplicationID))
	if err != nil {
		return err
	}
	if ea == nil {
		return nil
	}

	// 处理扩展信息
	extend := make(map[string]interface{})
	if ea.Extend != "" {
		if err := json.Unmarshal([]byte(ea.Extend), &extend); err != nil {
			return err
		}
	}

	// 添加真实开业时间到扩展信息
	if realOpenTime, exists := req.Data["real_open_time"]; exists {
		extend["real_open_time"] = realOpenTime
	}

	if channelPhotos, exists := req.Data["channel_photos"]; exists {
		// 如果是字符串数组，转换为JSON字符串存储
		if photosSlice, ok := channelPhotos.([]string); ok {
			photosJSON, err := json.Marshal(photosSlice)
			if err == nil {
				updateData["channel_photos"] = string(photosJSON)
			}
		} else if photosStr, ok := channelPhotos.(string); ok {
			updateData["channel_photos"] = photosStr
		}
	}

	if channelAdvice, exists := req.Data["channel_advice"]; exists {
		updateData["channel_advice"] = channelAdvice
	}

	// 更新扩展信息
	extendJSON, err := json.Marshal(extend)
	if err == nil {
		updateData["extend"] = string(extendJSON)
	}

	return nil
}

// GetName 获取处理器名称
func (p *WriteOffConfirmProcessor) GetName() string {
	return "writeoff_confirm"
}
