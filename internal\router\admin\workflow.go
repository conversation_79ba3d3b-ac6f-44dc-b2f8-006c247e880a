package admin

import (
	"marketing/internal/dao"
	"marketing/internal/dao/endpoint"
	endpointApplicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/dao/warranty"
	workflowHandler "marketing/internal/handler/admin/workflow"
	"marketing/internal/pkg/db"
	"marketing/internal/service/endpoint_application"
	workflowSvc "marketing/internal/service/workflow"

	"github.com/gin-gonic/gin"
)

// WorkflowRouter 通用工作流路由
type WorkflowRouter struct{}

// NewWorkflowRouter 创建工作流路由实例
func NewWorkflowRouter() *WorkflowRouter {
	return &WorkflowRouter{}
}

// Register 注册工作流路由
func (w *WorkflowRouter) Register(r *gin.RouterGroup) {
	// 通用工作流管理模块
	workflowGroup := r.Group("/workflow")
	{
		var Db = db.GetDB()

		// 初始化终端申请相关的依赖
		endpointApplyDao := endpointApplicationDao.NewEndpointApplyDao(Db)
		policyDao := endpointApplicationDao.NewEndpointPolicyDao(Db)
		endpointDao := endpoint.NewEndpointDao(Db)
		endpointTypeRepo := dao.NewEndpointTypeRepository(Db)
		configRepo := dao.NewConfigRepository(Db)
		subjectRepo := dao.NewGormTrainSubjectDao(Db)
		materialRepo := dao.NewMaterialDao()
		endpointImageDao := endpoint.NewEndpointImageDao(Db)
		warrantyRepo := warranty.NewWarrantyDao(Db)
		workflowRepo := endpointApplicationDao.NewWorkflowConfigDao(Db)

		// 初始化终端申请服务
		endpointApplicationService := endpoint_application.NewEndpointApplyService(
			endpointApplyDao,
			policyDao,
			endpointDao,
			endpointTypeRepo,
			configRepo,
			subjectRepo,
			materialRepo,
			endpointImageDao,
			warrantyRepo,
			workflowRepo,
		)

		// 初始化通用工作流服务
		workflowService := workflowSvc.NewWorkflowService(endpointApplicationService)

		// 初始化工作流Handler
		handler := workflowHandler.NewWorkflowHandler(workflowService)

		// 注册工作流路由
		workflowGroup.POST("/execute", handler.ExecuteWorkflow)              // 执行工作流
		workflowGroup.GET("/state", handler.GetWorkflowState)                // 获取工作流状态
		workflowGroup.GET("/available-actions", handler.GetAvailableActions) // 获取可用操作
	}
}
