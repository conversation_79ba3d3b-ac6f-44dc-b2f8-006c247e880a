# 批复表物料填充示例

本文档展示了 `DownloadWriteOffTable` 方法中物料数据填充的逻辑修改。

## 修改内容

### 修改前
```go
//查询物料支持明细
materialDetail, err := e.repo.GetEndpointMaterialSupportById(c, apply.ID)
if err != nil {
    return err
}

for _, line := range materialDetail {
    run.AddText(fmt.Sprintf("%s * %v", line.Name, line.Num))
    run.AddBreak()
}
```

### 修改后
```go
//查询物料支持明细
materialData, err := e.getMaterialData(c, apply)
if err != nil {
    log.Error("获取物料数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
    // 如果获取失败，继续处理但不添加物料信息
} else {
    // 根据不同格式填充物料信息
    if materialData.IsRawFormat {
        // 原始字符串格式
        run.AddText(materialData.RawDetail)
        run.AddBreak()
    } else {
        // 结构化格式
        for _, item := range materialData.MaterialList {
            run.AddText(fmt.Sprintf("%s * %d", item.Name, item.Quantity))
            run.AddBreak()
        }
    }
}
```

## 主要改进

### 1. 统一数据获取逻辑
- 使用与详情页相同的 `getMaterialData` 方法
- 支持新旧版本的物料数据格式
- 保持数据获取逻辑的一致性

### 2. 兼容性支持
- **新版本数据**：从 `endpoint_material_support` 表获取
- **Map 格式**：从 `extend` 字段的 `material_support_detail_map` 解析
- **字符串格式**：直接使用 `extend` 字段的 `material_support_detail`

### 3. 错误处理优化
- 如果获取物料数据失败，记录错误日志但不中断整个下载过程
- 提高了批复表生成的健壮性

## 填充效果示例

### 结构化格式
```
物料支持明细：
投影仪 * 2
音响设备 * 1
展示架 * 3
```

### 原始字符串格式
```
物料支持明细：
投影仪2台，音响设备1台，展示架3个，总价值15000元
```

## 优势

1. **一致性**：与详情页的物料数据获取逻辑完全一致
2. **兼容性**：支持所有历史数据格式
3. **健壮性**：错误处理更加完善，不会因为物料数据问题导致批复表下载失败
4. **可维护性**：复用了现有的物料数据解析逻辑，减少代码重复

这个修改确保了批复表中的物料信息与详情页显示的物料信息完全一致，无论数据存储在哪种格式中。
