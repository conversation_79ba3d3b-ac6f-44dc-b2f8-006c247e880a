package endpoint_application

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
)

type CreatedEndpointApplyReq struct {
	TopAgency       uint             `json:"top_agency" form:"top_agency" binding:"required"`
	SecondAgency    uint             `json:"second_agency" form:"second_agency"`
	Name            string           `json:"name" form:"name"`
	Type            int              `json:"type" form:"type"`
	Province        int              `json:"province" form:"province"`
	City            int              `json:"city" form:"city"`
	District        int              `json:"district" form:"district"`
	Address         string           `json:"address" form:"address"`
	Lng             string           `json:"lng" form:"lng"` //高德坐标系自己转换
	Lat             string           `json:"lat" form:"lat"`
	ChannelLevel    uint8            `json:"channel_level" form:"channel_level"`
	Blng            string           `json:"blng" form:"blat"`
	Blat            string           `json:"blat" form:"blat"`
	PolicyID        int              `json:"policy_id" form:"policy_id"`
	Phone           string           `json:"phone" form:"phone" binding:"phone"`
	Manager         string           `json:"manager" form:"manager"`
	ApplicationYear int              `json:"application_year"`
	Investor        string           `json:"investor" form:"investor"`                     //投资者
	InvestorPhone   string           `json:"investor_phone" form:"investor_phone"`         //投资者手机号
	Position        string           `json:"position" form:"position"`                     // 所处地段
	EndpointArea    string           `json:"endpoint_area" form:"endpoint_area"`           // 终端面积
	Pics            []types.OssPath  `json:"pics" form:"pics"`                             // 装修前实景图
	PicsExternal    []types.OssPath  `json:"pics_external,omitempty" form:"pics_external"` // 外部环境图
	PicsInternal    []types.OssPath  `json:"pics_internal,omitempty" form:"pics_internal"` // 内部环境图
	PicsDesign      []types.OssPath  `json:"pics_design,omitempty" form:"pics_design"`     // 平面图
	ExpectOpenTime  string           `json:"expect_open_time" form:"expect_open_time"`     // 期望开盘时间
	Extend          map[string]any   `json:"extend" form:"extend"`
	State           int              `json:"state"`
	CreatedAt       types.CustomTime `json:"created_at"`
}

type EndpointApplicationListReq struct {
	api.PaginationParams
	PolicyID     int    `json:"policy_id" form:"policy_id"`
	State        int    `json:"state" form:"state"`
	Name         string `json:"name" form:"name"`
	Type         int    `json:"type" form:"type"`
	Code         int    `json:"code" form:"code"`
	TopAgency    int    `json:"top_agency" form:"top_agency"`
	SecondAgency int    `json:"second_agency" form:"second_agency"`
}

type EndpointApplicationListResp struct {
	model.EndpointApplication
	PicsArr            []types.OssPath  `json:"pics" gorm:"-"`
	PicsExternalArr    []types.OssPath  `json:"pics_external" gorm:"-"`
	PicsInternalArr    []types.OssPath  `json:"pics_internal" gorm:"-"`
	PicsDesignArr      []types.OssPath  `json:"pics_design" gorm:"-"`
	ExtendMap          types.JSONMap    `json:"extend" gorm:"-"`
	Code               int              `json:"code"`
	EndpointID         int              `json:"endpoint_id"`
	EndpointCode       string           `json:"endpoint_code"`
	CreatedAtFormatted types.CustomTime `json:"created_at_formatted"`
	TopAgencyName      string           `json:"top_agency_name"`
	SecondAgencyName   string           `json:"second_agency_name"`
	EndpointTypeName   string           `json:"endpoint_type_name"`
	StateName          string           `json:"state_name"`
	//NextStateName      string           `json:"next_state_name"`
	// 工作流相关字段
	AvailableActions []ActionInfo `json:"available_actions" gorm:"-"`  // 当前状态可执行的操作
	CurrentStateInfo *StateInfo   `json:"current_state_info" gorm:"-"` // 当前状态详细信息
}

// StateInfo 状态信息
type StateInfo struct {
	ID   int    `json:"id"`   // 状态ID
	Name string `json:"name"` // 状态名称
	Type string `json:"type"` // 状态类型
}

// EndpointApplicationDetailResp 终端申请详情响应
type EndpointApplicationDetailResp struct {
	*EndpointApplicationListResp
	// 可以添加详情页特有的字段
	PostbackData *PostbackData `json:"postback_data,omitempty"` // 回传数据
	MaterialData *MaterialData `json:"material_data,omitempty"` // 物料数据
	AuditHistory []AuditRecord `json:"audit_history,omitempty"` // 审核历史
	WorkflowInfo *WorkflowInfo `json:"workflow_info"`           // 工作流信息
}

// PostbackData 回传数据
type PostbackData struct {
	WriteOffTable      types.OssPath    `json:"write_off_table"`
	LeaseContract      types.OssPath    `json:"lease_contract"`
	AnnualRent         float64          `json:"annual_rent"`
	DesignRenderings   []types.OssPath  `json:"design_renderings"`
	RenovationPhotos   []types.OssPath  `json:"renovation_photos"`
	RenovationVideos   []types.OssPath  `json:"renovation_videos"`
	Diploma            []types.OssPath  `json:"diploma"`
	EndpointGroupPhoto []types.OssPath  `json:"endpoint_group_photo"`
	ConfirmDate        types.CustomTime `json:"confirm_date"`
	Extend             types.JSONMap    `json:"extend"`
	AuditorName        string           `json:"auditor_name,omitempty"` // 审核人姓名
	AuditTime          string           `json:"audit_time,omitempty"`   // 审核时间
	AuditAction        string           `json:"audit_action,omitempty"` // 审核操作
}

// MaterialData 物料数据
type MaterialData struct {
	MaterialList  []MaterialItem         `json:"material_list"`
	TotalAmount   float64                `json:"total_amount"`
	RawDetail     string                 `json:"raw_detail,omitempty"`     // 原始字符串格式的物料详情
	IsRawFormat   bool                   `json:"is_raw_format,omitempty"`  // 是否为原始字符串格式
	AuditorName   string                 `json:"auditor_name,omitempty"`   // 审核人姓名
	AuditTime     string                 `json:"audit_time,omitempty"`     // 审核时间
	AuditAction   string                 `json:"audit_action,omitempty"`   // 审核操作
	SubmittedData map[string]interface{} `json:"submitted_data,omitempty"` // 提交的数据
}

// MaterialItem 物料项
type MaterialItem struct {
	ID       uint    `json:"id"`
	Name     string  `json:"name"`
	Quantity int     `json:"quantity"`
	Price    float64 `json:"price"`
	Amount   float64 `json:"amount"`
}

// AuditRecord 审核记录
type AuditRecord struct {
	ID            uint                   `json:"id"`
	Action        string                 `json:"action"`
	ActionName    string                 `json:"action_name"`
	FromState     int                    `json:"from_state"`
	ToState       int                    `json:"to_state"`
	Remark        string                 `json:"remark"`
	AuditorID     uint                   `json:"auditor_id"`
	AuditorName   string                 `json:"auditor_name"`
	CreatedAt     string                 `json:"created_at"`
	SubmittedData map[string]interface{} `json:"submitted_data,omitempty"` // 提交的数据
}

// WorkflowInfo 工作流信息
type WorkflowInfo struct {
	Name         string `json:"name"`          // 工作流名称
	Slug         string `json:"slug"`          // 工作流标识
	CurrentState int    `json:"current_state"` // 当前状态
	StartState   int    `json:"start_state"`   // 开始状态
}

type AuditApplyReq struct {
	ID          uint           `json:"id" form:"id"`
	State       int            `json:"state" form:"state" binding:"required"`
	Action      string         `json:"action" form:"action" binding:"required"`
	AuditAdvice string         `json:"audit_advice" form:"audit_advice"` // 建议
	AuditTime   string         `json:"audit_time" form:"audit_time"`
	AuditMan    string         `json:"audit_man"`
	Extend      map[string]any `json:"extend" form:"extend"`
}

type EndpointMaterialSupport struct {
	ID               uint    `json:"id"`
	Name             string  `json:"name"`
	Pic              string  `json:"pic"`
	Price            float64 `json:"price"`
	ProductionNumber string  `json:"production_number"`
	Thumbnail        string  `json:"thumbnail"`
	Num              int     `json:"num"`
}

type PostbackEndpointApplyReq struct {
	ID                 uint            `json:"id" form:"id"`
	Remark             string          `json:"remark" form:"remark"`
	Extend             map[string]any  `json:"extend" form:"extend"`
	WriteOffTable      types.OssPath   `json:"write_off_table" form:"write_off_table"`
	LeaseContract      types.OssPath   `json:"lease_contract" form:"lease_contract"`
	AnnualRent         float64         `json:"annual_rent" form:"annual_rent"`
	DesignRenderings   []types.OssPath `json:"design_renderings" form:"design_renderings"`
	RenovationPhotos   []types.OssPath `json:"renovation_photos" form:"renovation_photos"`
	RenovationVideos   []types.OssPath `json:"renovation_videos" form:"renovation_videos"`
	Diploma            []types.OssPath `json:"diploma" form:"diploma"`
	EndpointGroupPhoto []types.OssPath `json:"endpoint_group_photo" form:"endpoint_group_photo"`
}

// WriteOffReq 核销请求
type WriteOffReq struct {
	ID               uint           `json:"id" form:"id"`
	State            int            `json:"state" form:"state" binding:"required"` // 审核状态
	Action           string         `json:"action" form:"action" binding:"required"`
	WriteOffAdvice   string         `json:"write_off_advice" form:"write_off_advice"`     // 核销建议
	Pay              string         `json:"pay" form:"pay"`                               // 支付金额
	BagSupportAmount float64        `json:"bag_support_amount" form:"bag_support_amount"` // 书包支持金额
	Extend           map[string]any `json:"extend" form:"extend"`                         // 扩展字段
}

// ChannelAuditReq 渠道审核请求
type ChannelAuditReq struct {
	ID            uint           `json:"id" form:"id"`
	State         int            `json:"state" form:"state" binding:"required"` // 审核状态
	Action        string         `json:"action" form:"action" binding:"required"`
	ChannelPhotos []string       `json:"channel_photos" form:"channel_photos"` // 渠道照片
	ChannelAdvice string         `json:"channel_advice" form:"channel_advice"` // 渠道建议
	RealOpenTime  int64          `json:"real_open_time" form:"real_open_time"` // 真实开业时间
	Extend        map[string]any `json:"extend" form:"extend"`                 // 扩展字段
}

type WorkflowStepDetail struct {
	Detail           CreatedEndpointApplyReq `json:"detail"`                      // 申请详情
	AvailableActions []ActionInfo            `json:"available_actions,omitempty"` // 可用操作

	Audit        *AuditApplyReq    `json:"audit,omitempty"`         // audit 类型状态的数据
	Material     *MaterialData     `json:"material,omitempty"`      // material 类型状态的数据
	Postback     *PostbackData     `json:"postback,omitempty"`      // postback 类型状态的数据
	WriteOff     *WriteOffData     `json:"writeoff,omitempty"`      // writeoff 类型状态的数据
	ChannelAudit *ChannelAuditData `json:"channel_audit,omitempty"` // channel_audit 类型状态的数据
}

// StateTypeData 状态类型数据结构
type StateTypeData struct {
	Audit        *AuditData        `json:"audit,omitempty"`         // audit 类型状态的数据
	Material     *MaterialData     `json:"material,omitempty"`      // material 类型状态的数据
	Postback     *PostbackData     `json:"postback,omitempty"`      // postback 类型状态的数据
	WriteOff     *WriteOffData     `json:"writeoff,omitempty"`      // writeoff 类型状态的数据
	ChannelAudit *ChannelAuditData `json:"channel_audit,omitempty"` // channel_audit 类型状态的数据
}

// AuditData 审核类型状态数据
type AuditData struct {
	AuditAdvice string `json:"audit_advice"` // 审核建议
	AuditRemark string `json:"audit_remark"` // 审核备注
	AuditorName string `json:"auditor_name"` // 审核人姓名
	AuditTime   string `json:"audit_time"`   // 审核时间
	AuditAction string `json:"audit_action"` // 审核操作
}

// WriteOffData 核销类型状态数据
type WriteOffData struct {
	WriteOffAdvice   string                 `json:"write_off_advice"`         // 核销建议
	Pay              string                 `json:"pay"`                      // 支付金额
	BagSupportAmount float64                `json:"bag_support_amount"`       // 书包支持金额
	AuditorName      string                 `json:"auditor_name,omitempty"`   // 审核人姓名
	AuditTime        string                 `json:"audit_time,omitempty"`     // 审核时间
	AuditAction      string                 `json:"audit_action,omitempty"`   // 审核操作
	SubmittedData    map[string]interface{} `json:"submitted_data,omitempty"` // 提交的数据
}

// ChannelAuditData 渠道审核类型状态数据
type ChannelAuditData struct {
	ChannelPhotos []string               `json:"channel_photos"`           // 渠道照片
	ChannelAdvice string                 `json:"channel_advice"`           // 渠道建议
	RealOpenTime  int64                  `json:"real_open_time"`           // 真实开业时间
	AuditorName   string                 `json:"auditor_name,omitempty"`   // 审核人姓名
	AuditTime     string                 `json:"audit_time,omitempty"`     // 审核时间
	AuditAction   string                 `json:"audit_action,omitempty"`   // 审核操作
	SubmittedData map[string]interface{} `json:"submitted_data,omitempty"` // 提交的数据
}

// StatusRecordWithUser 状态记录与用户信息
type StatusRecordWithUser struct {
	ID            uint   `json:"id" gorm:"column:id"`
	ApplicationID int    `json:"application_id" gorm:"column:application_id"`
	BeforeState   int    `json:"before_state" gorm:"column:before_state"`
	AfterState    int    `json:"after_state" gorm:"column:after_state"`
	Extend        string `json:"extend" gorm:"column:extend"`
	HandlerID     int    `json:"handler_id" gorm:"column:handler_id"`
	Remark        string `json:"remark" gorm:"column:remark"`
	CreatedAt     string `json:"created_at" gorm:"column:created_at"`
	HandlerName   string `json:"handler_name" gorm:"column:handler_name"`
}

// CompletedData 完成状态数据
type CompletedData struct {
	Message     string `json:"message"`      // 完成消息
	CompletedAt string `json:"completed_at"` // 完成时间
}

// ResultData 结果状态数据（拒绝状态）
type ResultData struct {
	State     int    `json:"state"`      // 状态ID
	Message   string `json:"message"`    // 状态消息
	Advice    string `json:"advice"`     // 审核建议
	UpdatedAt string `json:"updated_at"` // 更新时间
}

// CanceledData 取消状态数据
type CanceledData struct {
	Message    string `json:"message"`     // 取消消息
	Reason     string `json:"reason"`      // 取消原因
	CanceledAt string `json:"canceled_at"` // 取消时间
}

// TerminateReq 建店终止请求
type TerminateReq struct {
	ID              uint           `json:"id" form:"id"`
	TerminateReason string         `json:"terminate_reason" form:"terminate_reason" binding:"required"` // 终止原因
	Remark          string         `json:"remark" form:"remark"`                                        // 备注
	Extend          map[string]any `json:"extend" form:"extend"`                                        // 扩展字段
}

type LatestEndpointImageResp struct {
	AutoTime   types.CustomTime `json:"auto_time"`
	ManualTime types.CustomTime `json:"manual_time"`
}

// AccountRecordReq 入账请求
type AccountRecordReq struct {
	ID            uint   `json:"id" form:"id"`                         // 分期ID
	SupportStatus uint8  `json:"support_status" form:"support_status"` // 入账状态：1-已入账，2-中止
	RecordRemark  string `json:"record_remark" form:"record_remark"`   // 入账备注
}

// InstallmentListReq 分期列表请求
type InstallmentListReq struct {
	api.PaginationParams
	ApplicationID uint   `json:"application_id" form:"application_id"` // 申请ID
	Year          uint16 `json:"year" form:"year"`                     // 年份
	Month         uint8  `json:"month" form:"month"`                   // 月份
	SupportStatus *uint8 `json:"support_status" form:"support_status"` // 入账状态：0-未入账，1-已入账，2-中止
}

// InstallmentsByApplicationReq 根据申请ID获取分期列表请求（不分页）
type InstallmentsByApplicationReq struct {
	ApplicationID uint `json:"application_id" form:"application_id" binding:"required"` // 申请ID
}

// InstallmentListResp 分期列表响应
type InstallmentListResp struct {
	ID                 uint             `json:"id"`
	ApplicationID      uint             `json:"application_id"`
	ApplicationName    string           `json:"application_name"` // 申请名称
	Year               uint16           `json:"year"`
	Month              uint8            `json:"month"`
	Date               types.DateOnly   `json:"date"`
	Fee                uint             `json:"fee"`                  // 支持金额（分）
	StaffFee           *uint            `json:"staff_fee"`            // 人员支持金额（分）
	SupportStatus      uint8            `json:"support_status"`       // 入账状态
	StaffSupportStatus uint8            `json:"staff_support_status"` // 支持人员入账状态
	StaffRecordRemark  string           `json:"staff_record_remark"`
	RecordedBy         uint             `json:"recorded_by"`   // 入账人
	RecordedAt         types.CustomTime `json:"recorded_at"`   // 入账时间
	RecordRemark       string           `json:"record_remark"` // 入账备注
	CreatedAt          types.CustomTime `json:"created_at"`
	UpdatedAt          types.CustomTime `json:"updated_at"`

	// 扩展字段
	SupportStatusName string `json:"support_status_name"` // 入账状态名称
	RecordedByName    string `json:"recorded_by_name"`    // 入账人姓名
}
