package processors

import (
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/statemachine"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// PostbackProcessor 回传处理器
type PostbackProcessor struct{}

// NewPostbackProcessor 创建回传处理器
func NewPostbackProcessor() *PostbackProcessor {
	return &PostbackProcessor{}
}

// Process 处理建店回传状态 - 直接执行回传逻辑
func (p *PostbackProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
	// 获取事务DAO
	txRepo, ok := req.Data["txRepo"].(applicationDao.EndpointApplyDao)
	if !ok {
		return nil
	}

	// 验证申请状态（可选，如果状态机已经验证过了）
	if err := p.validateApplicationState(req); err != nil {
		return err
	}

	// 处理建店回传逻辑
	return p.handlePostback(ctx, txRepo, req, updateData)
}

// handlePostback 处理建店回传完成状态
func (p *PostbackProcessor) handlePostback(ctx *gin.Context, txRepo applicationDao.EndpointApplyDao, req *statemachine.StateTransitionRequest, updateData map[string]any) error {

	// 保存回传数据到专门的表
	postback := &model.EndpointApplicationPostback{
		ApplicationID: int(req.ApplicationID),
	}

	// 从请求数据中提取回传信息
	if writeOffTable, exists := req.Data["write_off_table"]; exists {
		postback.WriteOffTable = cast.ToString(writeOffTable)
	}
	if leaseContract, exists := req.Data["lease_contract"]; exists {
		postback.LeaseContract = cast.ToString(leaseContract)
	}
	if annualRent, exists := req.Data["annual_rent"]; exists {
		postback.AnnualRent = cast.ToFloat64(annualRent)
	}
	if designRenderings, exists := req.Data["design_renderings"]; exists {
		postback.DesignRenderings = utils.JsonMarshals(designRenderings)
	}
	if renovationPhotos, exists := req.Data["renovation_photos"]; exists {
		postback.RenovationPhotos = utils.JsonMarshals(renovationPhotos)
	}
	if renovationVideos, exists := req.Data["renovation_videos"]; exists {
		postback.RenovationVideos = utils.JsonMarshals(renovationVideos)
	}
	if diploma, exists := req.Data["diploma"]; exists {
		postback.Diploma = utils.JsonMarshals(diploma)
	}
	if endpointGroupPhoto, exists := req.Data["endpoint_group_photo"]; exists {
		postback.EndpointGroupPhoto = utils.JsonMarshals(endpointGroupPhoto)
	}
	if extend, exists := req.Data["extend"]; exists {
		postback.Extend = extend.(map[string]interface{})
	}

	// 保存回传数据
	return txRepo.UpsertEndpointApplyPostback(ctx, int(req.ApplicationID), postback)
}

// validateApplicationState 验证申请状态
func (p *PostbackProcessor) validateApplicationState(req *statemachine.StateTransitionRequest) error {
	// 从请求数据中获取申请信息
	_, exists := req.Data["application"]
	if !exists {
		return nil // 如果没有申请信息，跳过验证
	}

	// 这里可以添加具体的状态验证逻辑
	// 例如：检查当前状态是否允许进行回传操作
	// 由于状态机已经处理了状态验证，这里主要用于业务逻辑验证

	return nil
}

// GetName 获取处理器名称
func (p *PostbackProcessor) GetName() string {
	return "postback"
}
