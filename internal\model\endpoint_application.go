package model

import (
	"marketing/internal/pkg/types"
	"time"
)

type EndpointApplication struct {
	ID              int            `gorm:"column:id;type:int;primaryKey;autoIncrement" json:"id"`
	TopAgency       uint           `gorm:"column:top_agency;type:int;not null;default:0;comment:'总代id'" json:"top_agency"`
	SecondAgency    uint           `gorm:"column:second_agency;type:int;not null;default:0;comment:'二代id'" json:"second_agency"`
	Name            string         `gorm:"column:name;type:varchar(100);not null;comment:'终端名称'" json:"name"`
	Type            int            `gorm:"column:type;type:tinyint;not null;default:0;comment:'终端分类信息（1:专柜、2:运营商渠道、3:专卖店，4:城市综合体）'" json:"type"`
	Province        int            `gorm:"column:province;type:int;not null;comment:'省'" json:"province"`
	City            int            `gorm:"column:city;type:int;not null;comment:'市'" json:"city"`
	District        int            `gorm:"column:district;type:int;not null;comment:'区'" json:"district"`
	Address         string         `gorm:"column:address;type:varchar(512);not null;comment:'详细地址'" json:"address"`
	Lng             string         `gorm:"column:lng;type:varchar(20);comment:'经度（高德坐标）'" json:"lng"`
	Lat             string         `gorm:"column:lat;type:varchar(20);comment:'纬度（高德坐标）'" json:"lat"`
	ChannelLevel    uint8          `gorm:"column:channel_level;type:tinyint unsigned;not null;default:0;comment:'渠道等级，0-未知，1-省会级，2-地市级，3-县级，4-镇级'" json:"channel_level"`
	Blng            string         `gorm:"column:blng;type:varchar(20);default:null;comment:'经度（百度坐标）'" json:"blng"`
	Blat            string         `gorm:"column:blat;type:varchar(20);default:null;comment:'纬度（百度坐标）'" json:"blat"`
	PolicyID        int            `gorm:"column:policy_id;type:int;not null;default:0;comment:'终端支持政策id,0代表没选或者是综合体终端的申请'" json:"policy_id"`
	Phone           *string        `gorm:"column:phone;type:varchar(20);default:null;comment:'电话'" json:"phone"`
	Manager         string         `gorm:"column:manager;type:varchar(50);not null;comment:'负责人'" json:"manager"`
	Investor        string         `gorm:"column:investor;type:varchar(50);default null;comment:'投资者'" json:"investor"`
	InvestorPhone   string         `gorm:"column:investor_phone;type:varchar(20);default null;comment:'投资者手机号'" json:"investor_phone"`
	ExpectOpenTime  types.DateOnly `gorm:"column:expect_open_time;type:varchar(50);default null;comment:'预计开业时间'" json:"expect_open_time"`
	Position        string         `gorm:"column:position;type:varchar(50);default null;comment:'所处地段'" json:"position"`
	EndpointArea    string         `gorm:"column:endpoint_area;type:varchar(50);default null;comment:'终端面积'" json:"endpoint_area"`
	Pics            string         `gorm:"column:pics;type:varchar(512);default null;comment:'装修前实景图'" json:"-"`
	PicsExternal    string         `gorm:"column:pics_external;type:text;not null;comment:'外部环境图'" json:"-"`
	PicsInternal    string         `gorm:"column:pics_internal;type:text;not null;comment:'内部环境图'" json:"-"`
	PicsDesign      string         `gorm:"column:pics_design;type:text;not null;comment:'平面图'" json:"-"`
	Extend          string         `gorm:"column:extend;type:varchar(512);default null;comment:'扩展信息'" json:"-"`
	ApplyType       string         `gorm:"column:apply_type;type:varchar(20);default null;comment:'申请类型'" json:"apply_type"`
	Pay             string         `gorm:"column:pay;type:varchar(50);comment:'支付金额'" json:"pay"`
	AddToEndpointId uint           `gorm:"column:add_to_endpoint_id;type:int;default 0;comment:'添加到的终端id'" json:"add_to_endpoint_id"`
	State           int            `gorm:"column:state;type:int;default 0;comment:'新的状态'" json:"state"`
	NextState       int            `gorm:"column:next_state;type:int;default 0;comment:'下一个状态'" json:"next_state"`
	ApplicationYear int            `gorm:"column:application_year;type:int;default null;comment:'申请年份'" json:"application_year"`
	//回传相关字段不返回给前端
	WriteOffTable      string  `json:"-"`
	LeaseContract      string  `json:"-"`
	AnnualRent         float64 `json:"-"`
	DesignRenderings   string  `json:"-"`
	RenovationPhotos   string  `json:"-"`
	RenovationVideos   string  `json:"-"`
	Diploma            string  `json:"-"`
	EndpointGroupPhoto string  `json:"-"`
	//建店审核相关的字段
	AuditMan    string           `gorm:"type:varchar(20);not null;comment:审核人;collate:utf8_general_ci"`   // 审核人
	AuditAdvice string           `gorm:"type:varchar(255);not null;comment:审核意见;collate:utf8_general_ci"` // 审核意见
	AuditTime   types.CustomTime `gorm:"type:timestamp;default:null;comment:审核时间"`                        // 审核时间
	// 渠道审核相关字段
	ChannelAuditedBy *uint            `gorm:"column:channel_audited_by;type:int;default:null;comment:'渠道审核人ID'" json:"channel_audited_by"`
	ChannelPhotos    string           `gorm:"column:channel_photos;type:text;default:null;comment:'渠道照片'" json:"channel_photos"`
	ChannelAdvice    string           `gorm:"column:channel_advice;type:varchar(500);default:null;comment:'渠道建议'" json:"channel_advice"`
	ChannelAuditedAt types.CustomTime `gorm:"column:channel_audited_at;type:datetime;default:null;comment:'渠道审核时间'" json:"channel_audited_at"`
	// 核销相关字段
	WriteOffAdvice   string           `gorm:"column:write_off_advice;type:varchar(500);default:null;comment:'核销建议'" json:"write_off_advice"`
	BagSupportAmount *float64         `gorm:"column:bag_support_amount;type:decimal(10,2);default:null;comment:'物料支持金额'" json:"bag_support_amount"`
	WriteOffTime     types.CustomTime `gorm:"column:write_off_time;type:datetime;default:null;comment:'核销时间'" json:"write_off_time"`
	WriteOffMan      *string          `gorm:"column:write_off_man;type:varchar(100);default:null;comment:'核销人'" json:"write_off_man"`
	WriteOffManName  string           `gorm:"-" json:"write_off_man_name"`
	CreatedAt        time.Time        `gorm:"column:created_at" json:"-"`
	UpdatedAt        types.CustomTime `gorm:"column:updated_at" json:"-"`
}

// EndpointApplicationInstallment represents the endpoint_application_installment table in the database.
type EndpointApplicationInstallment struct {
	ID                   uint       `gorm:"primaryKey;autoIncrement"`
	ApplicationID        uint       `gorm:"not null;comment:'申请id，对应endpoint_application'"`
	Year                 uint16     `gorm:"not null"`
	Month                uint8      `gorm:"not null"`
	Date                 time.Time  `gorm:"not null;comment:'year+month，方便查询'"`
	Fee                  uint       `gorm:"not null;comment:'支持金额，分'"`
	StaffFee             *uint      `gorm:"comment:'人员支持金额（分）'"`
	MissionScore         *uint      `gorm:"comment:'任务考核分数'"`
	RentProof            string     `gorm:"type:text;not null;comment:'租金凭证图'"`
	RentProofAuditState  uint8      `gorm:"not null;default:0;comment:'租金凭证审核状态，0-未审核，1-通过，2-不通过'"`
	RentProofAuditRemark string     `gorm:"type:varchar(100);not null;default:''"`
	RentProofUpdatedAt   *time.Time `gorm:"comment:'凭证上传时间'"`
	SupportStatus        uint8      `gorm:"not null;default:0;comment:'0-未入账，1-已入账，2-中止'"`
	StaffSupportStatus   uint8      `gorm:"not null;default:0;comment:'支持人员入账状态，0-未入账，1-已入账，2-中止'"`
	StaffRecordRemark    string     `gorm:"type:varchar(100);not null;default:'';comment:'支持人员入账备注'"`
	RecordedBy           uint       `gorm:"not null;default:0;comment:'入账人'"`
	RecordedAt           time.Time  `gorm:"comment:'入账时间'"`
	RecordRemark         string     `gorm:"type:varchar(100);not null;default:'';comment:'入账备注'"`
	CreatedAt            time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt            time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP"`
	AuditStatus          int        `gorm:"not null;default:0;comment:'核销审核状态 0-待审核，1-审核中，2-审核通过，-1审核不通过'"`
	Audit1               string     `gorm:"type:text;not null;comment:'核销审核1'"`
	Audit2               string     `gorm:"type:text;not null;comment:'核销审核2'"`
	Stage                int        `gorm:"not null;default:0;comment:'阶段'"`
}
