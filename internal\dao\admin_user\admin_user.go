package admin_user

import (
	"context"
	"errors"
	"marketing/internal/consts"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"slices"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"go.uber.org/zap"
)

// UserDao 用户数据访问接口
type UserDao interface {
	GetByID(ctx context.Context, id uint) (*model.AdminUsers, error)
	GetByIDs(ctx context.Context, ids []uint) ([]model.AdminUsers, error)
	GetMapByIDs(ctx context.Context, ids []uint) (map[uint]model.AdminUsers, error)
	GetByUsername(ctx context.Context, username string) (*model.AdminUsers, error)
	GetByPhone(ctx context.Context, phone string) (*model.AdminUsers, error)
	GetByJobNumber(ctx context.Context, jobNumber string) (*model.AdminUsers, error)
	GetByQwUserID(ctx context.Context, QwUserID string) (*model.AdminUsers, error)
	GetUserWithAll(ctx context.Context, id uint, systemType string) (*model.AdminUsers, error)
	GetUserWithRoles(ctx context.Context, id uint) (*model.AdminUsers, error)
	GetUserRoleIDs(ctx context.Context, id uint) ([]uint, error)
	InsertUserLog(ctx context.Context, log *model.AdminUserLog) error
	UpdateOrCreateEmployeeUser(ctx context.Context, user *model.EmployeeUser) error
	UserAgency(ctx context.Context, id uint) (*model.Agency, error)
	UserEndpoint(ctx context.Context, id uint) (*model.Endpoint, error)
	GetUserGroups(ctx context.Context, user *model.AdminUsers) (*model.AdminUserGroupV2, error)
	UpdateUserActiveTime(ctx context.Context, id uint) error

	GetUserID(ctx context.Context, endpoint int) (uint, error)
	GetSalesmanByUserID(ctx context.Context, id uint) (string, error)
	GetTopAgencyUsersByAgency(ctx context.Context, topAgency uint) ([]uint, error)
}

// userDaoImpl 实现 UserDao 接口
type userDaoImpl struct {
	db *gorm.DB
}

// NewUserDao 创建 UserDao 实��
func NewUserDao(db *gorm.DB) UserDao {
	return &userDaoImpl{
		db: db,
	}
}

// GetByID 根据ID获取用户基本信息
func (d *userDaoImpl) GetByID(ctx context.Context, id uint) (*model.AdminUsers, error) {
	var user model.AdminUsers
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *userDaoImpl) GetByIDs(ctx context.Context, ids []uint) ([]model.AdminUsers, error) {
	var users []model.AdminUsers
	if err := d.db.WithContext(ctx).Where("id IN ?", ids).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

func (d *userDaoImpl) GetMapByIDs(ctx context.Context, ids []uint) (map[uint]model.AdminUsers, error) {
	users, err := d.GetByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}
	userMap := make(map[uint]model.AdminUsers)
	for _, user := range users {
		userMap[user.ID] = user
	}
	return userMap, nil
}

func (d *userDaoImpl) GetByUsername(ctx context.Context, username string) (*model.AdminUsers, error) {
	var user model.AdminUsers
	if err := d.db.WithContext(ctx).First(&user, "username = ?", username).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *userDaoImpl) GetByPhone(ctx context.Context, phone string) (*model.AdminUsers, error) {
	var user model.AdminUsers
	if err := d.db.WithContext(ctx).First(&user, "phone = ?", phone).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *userDaoImpl) GetByJobNumber(ctx context.Context, jobNumber string) (*model.AdminUsers, error) {
	var user model.AdminUsers
	if err := d.db.WithContext(ctx).Table("admin_users u").Joins("left join employee_user as e on e.uid=u.id").
		Where("job_number = ?", jobNumber).
		Where("u.status = ?", 1).
		Select("u.*").First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *userDaoImpl) GetByQwUserID(ctx context.Context, QwUserID string) (*model.AdminUsers, error) {
	var user model.AdminUsers
	if err := d.db.WithContext(ctx).First(&user, "qw_userid = ?", QwUserID).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserWithRoles 获取用及其角色信息,包括用户组
func (d *userDaoImpl) GetUserWithRoles(ctx context.Context, id uint) (*model.AdminUsers, error) {
	user, err := d.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	user = d.withUserGroup(ctx, user)
	return d.withRoles(ctx, user, true), nil
}

// GetUserRoleIDs 获取用及其角色信息
func (d *userDaoImpl) GetUserRoleIDs(ctx context.Context, id uint) ([]uint, error) {
	user, err := d.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	user = d.withRoles(ctx, user, false)

	var roleIDs []uint
	for _, role := range user.Roles {
		roleIDs = append(roleIDs, role.ID)
	}
	return roleIDs, nil
}

// UpdateOrCreateEmployeeUser 更新用户企微信息（读书郎教育科技）
func (d *userDaoImpl) UpdateOrCreateEmployeeUser(ctx context.Context, user *model.EmployeeUser) error {
	return d.db.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "uid"}}, // 指定冲突检测的列
			UpdateAll: true,                           // 更新所有列
		}).
		Create(user).
		Error
}

// GetUserWithAll 获取用户的所有信息(包括角色、权限、菜单)
func (d *userDaoImpl) GetUserWithAll(ctx context.Context, id uint, systemType string) (*model.AdminUsers, error) {
	user, err := d.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	user = d.withUserGroup(ctx, user)
	user = d.withRoles(ctx, user, true)
	user = d.withPermissions(ctx, user)
	user = d.withMenus(ctx, user, systemType)
	user = d.withJobNumber(ctx, user)
	user = d.withResourceGroup(ctx, user)
	user = d.withApps(ctx, user, systemType)

	return user, nil
}

func (d *userDaoImpl) UserAgency(ctx context.Context, id uint) (*model.Agency, error) {
	joinColumn := "second_agency"
	user, err := d.GetUserWithRoles(ctx, id)
	if err != nil {
		return nil, err
	}
	var roles []string
	for _, role := range user.Roles {
		roles = append(roles, role.Slug)
	}
	if slices.Contains(roles, consts.RoleTopAgency) || slices.Contains(roles, consts.RoleTopAgencyAssistant) {
		joinColumn = "top_agency"
	}

	var agency model.Agency
	err = d.db.Table("user_agency ua").
		Joins("INNER JOIN agency a ON ua."+joinColumn+" = a.id").
		Where("ua.uid = ?", id).
		Where("ua.status =?", 1).
		Select("a.*").
		Scan(&agency).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return &agency, nil
}

func (d *userDaoImpl) UserEndpoint(ctx context.Context, id uint) (*model.Endpoint, error) {
	var endpoint model.Endpoint
	if err := d.db.WithContext(ctx).
		Joins("INNER JOIN user_endpoint ue ON ue.endpoint = endpoint.id ").
		Where("ue.uid = ?", id).
		Where("endpoint.status = ?", 1).
		Select("endpoint.*").
		First(&endpoint).Error; err != nil {
		return nil, err
	}
	return &endpoint, nil
}

func (d *userDaoImpl) GetUserID(ctx context.Context, endpoint int) (uint, error) {
	var userID int
	err := d.db.WithContext(ctx).
		Table("user_endpoint").
		Where("endpoint = ? AND user_type = ? AND role = ?", endpoint, "user", consts.UeRoleManager).
		Pluck("uid", &userID).Error
	if err != nil || userID == 0 {
		return 0, err
	}
	return uint(userID), nil
}

func (d *userDaoImpl) GetSalesmanByUserID(ctx context.Context, id uint) (string, error) {
	var salesman string
	err := d.db.WithContext(ctx).Table("admin_users").
		Where("id = ?", id).
		Pluck("name", &salesman).Error
	if err != nil || salesman == "" {
		return "", err
	}
	return salesman, nil
}

// GetUserGroups 获取用户的所有用户组信息(目前默认一个用户一个用户组 后面逻辑有修改需要变动)
func (d *userDaoImpl) GetUserGroups(ctx context.Context, user *model.AdminUsers) (*model.AdminUserGroupV2, error) {
	user = d.withUserGroup(ctx, user)
	if len(user.UserGroups) == 0 {
		return nil, nil
	}
	res := user.UserGroups[0]
	return &res, nil
}

// UpdateUserActiveTime 更新用户活跃状态
func (d *userDaoImpl) UpdateUserActiveTime(ctx context.Context, id uint) error {
	return d.db.WithContext(ctx).Model(&model.AdminUsers{}).Where("id = ?", id).Update("actived_at", time.Now().Format(time.DateTime)).Error
}

// withRoles 获取用户角色信息
func (d *userDaoImpl) withRoles(ctx context.Context, user *model.AdminUsers, inheritGroupRoles bool) *model.AdminUsers {
	var roles, groupRoles []model.AdminRoles
	// 继承用户组的角色（去掉了）
	var groupRoleID []uint
	if len(groupRoleID) > 0 {
		err := d.db.WithContext(ctx).Where("id IN ?", groupRoleID).Find(&groupRoles).Error
		if err != nil {
			log.Error("获取用户组角色失败", zap.Error(err))
			return user
		}
	}
	err := d.db.WithContext(ctx).
		Table("admin_users u").
		Select("r.*").
		Joins("left join admin_role_users ru ON u.id = ru.user_id").
		Joins("left join admin_roles r ON ru.role_id = r.id AND r.deleted_at IS NULL").
		Where("u.id = ?", user.ID).
		Where("r.status=1").
		Group("r.id").
		Scan(&roles).Error
	if err != nil {
		log.Error("获取用户角色失败", zap.Error(err))
		return user
	}
	if inheritGroupRoles {
		user.Roles = append(groupRoles, roles...)
	} else {
		user.Roles = roles
	}
	return user
}

// withPermissions 获取用户权限信息
func (d *userDaoImpl) withPermissions(ctx context.Context, user *model.AdminUsers) *model.AdminUsers {
	var permissions []model.AdminPermissionV2
	if len(user.Roles) > 0 {
		var roleIds []uint
		for _, role := range user.Roles {
			roleIds = append(roleIds, role.ID)
		}

		err := d.db.WithContext(ctx).
			Table("admin_role_permissions_v2 rp").
			Select("p.*").
			Joins("JOIN admin_permissions_v2 p ON p.id = rp.permission_id").
			Where("rp.role_id IN ?", roleIds).
			Where("rp.deleted_at IS NULL").
			Where("p.deleted_at IS NULL").
			Group("p.id").
			Scan(&permissions).Error
		if err != nil {
			log.Error("获取用户权限失败", zap.Error(err))
			return user
		}
	}
	user.Permissions = permissions
	return user
}

// withMenus 获取用户菜单信息
func (d *userDaoImpl) withMenus(ctx context.Context, user *model.AdminUsers, systemType string) *model.AdminUsers {
	var menus []model.AdminMenuV2
	var err error
	isAdministrator := false
	if len(user.Roles) > 0 {
		var roleIds []uint
		for _, role := range user.Roles {
			if role.Slug == "administrator" {
				isAdministrator = true
			}
			roleIds = append(roleIds, role.ID)
		}
		if isAdministrator {
			err = d.db.WithContext(ctx).Table("admin_menus_v2 m").Select("m.*").
				Where("m.deleted_at IS NULL").
				Where("m.system_type = ?", systemType).Scan(&menus).Error
		} else {
			err = d.db.WithContext(ctx).
				Table("admin_role_menus_v2 rm").
				Select("m.*").
				Joins("JOIN admin_menus_v2 m ON m.id = rm.menu_id").
				Where("m.system_type = ?", systemType).Where("rm.role_id IN ?", roleIds).
				Where("rm.deleted_at IS NULL").
				Where("m.deleted_at IS NULL").
				Group("m.id").
				Scan(&menus).Error
		}
		if err != nil {
			log.Error("获取用户菜单失败", zap.Error(err))
			return user
		}
	}
	user.Menus = menus
	return user
}

// withJobNumber 获取用户的工号信息
func (d *userDaoImpl) withJobNumber(ctx context.Context, user *model.AdminUsers) *model.AdminUsers {
	var employeeUser model.EmployeeUser
	if err := d.db.WithContext(ctx).First(&employeeUser, "uid =?", user.ID).Error; err != nil {
		return user
	}
	user.JobNumber = employeeUser.JobNumber
	return user
}

// withResourceGroup 获取用户的资源组信息(用之前需要获取用户的角色信息)
func (d *userDaoImpl) withResourceGroup(ctx context.Context, user *model.AdminUsers) *model.AdminUsers {
	var roleIDs []uint
	for _, role := range user.Roles {
		roleIDs = append(roleIDs, role.ID)
	}
	var resourceGroup []uint
	err := d.db.WithContext(ctx).
		Model(model.ResourceGroup{}).
		Joins("JOIN admin_role_resources ON admin_role_resources.group_id = resource_groups.id").
		Select("group_id").
		Where("role_id in (?)", roleIDs).
		Pluck("group_id", &resourceGroup).Error
	if err != nil {
		log.Error("获取用户资源组失败", zap.Error(err))
		return user
	}
	user.ResourceGroup = resourceGroup
	return user
}

// withUserGroup 获取用户的用户组信息
func (d *userDaoImpl) withUserGroup(ctx context.Context, user *model.AdminUsers) *model.AdminUsers {
	var userGroup []model.AdminUserGroupV2
	err := d.db.WithContext(ctx).
		Table("admin_user_group_v2 as g").
		Joins("JOIN admin_user_user_group_v2 ug ON ug.user_group_id = g.id").
		Select("g.*").
		Where("ug.user_id = ?", user.ID).
		Where("g.deleted_at IS NULL").
		Scan(&userGroup).Error
	if err != nil {
		log.Error("获取用户用户组失败", zap.Error(err))
		return user
	}
	user.UserGroups = userGroup
	return user
}

func (d *userDaoImpl) withApps(ctx context.Context, user *model.AdminUsers, systemType string) *model.AdminUsers {
	var apps []model.AppSystemV2
	var err error
	isAdministrator := false
	if len(user.Roles) > 0 {
		var roleIds []uint
		for _, role := range user.Roles {
			if role.Slug == "administrator" {
				isAdministrator = true
			}
			roleIds = append(roleIds, role.ID)
		}
		if isAdministrator {
			err = d.db.WithContext(ctx).Table("app_system_v2 m").Select("m.*").
				Where("m.visibility = 1").
				Where("m.parent = ?", systemType).Scan(&apps).Error
		} else {
			err = d.db.WithContext(ctx).
				Table("admin_role_apps_v2 rm").
				Select("m.*").
				Joins("JOIN app_system_v2 m ON m.id = rm.app_id").
				Where("m.parent = ?", systemType).Where("rm.role_id IN ?", roleIds).
				Where("m.visibility = 1").
				Group("m.id").
				Scan(&apps).Error
		}
		if err != nil {
			log.Error("获取用户app失败", zap.Error(err))
			return user
		}
	}
	user.UserApps = apps
	return user
}

// InsertUserLog 插入用户日志
func (d *userDaoImpl) InsertUserLog(ctx context.Context, log *model.AdminUserLog) error {
	return d.db.WithContext(ctx).Create(log).Error
}

// GetTopAgencyUsersByAgency 根据总代ID获取相关的总代用户ID列表
func (d *userDaoImpl) GetTopAgencyUsersByAgency(ctx context.Context, topAgency uint) ([]uint, error) {
	var userIDs []uint
	err := d.db.WithContext(ctx).Table("admin_role_users as ru").
		Select("ru.user_id").
		Joins("LEFT JOIN user_agency ua ON ua.uid = ru.user_id").
		Joins("LEFT JOIN admin_roles r ON ru.role_id = r.id").
		Where("ua.top_agency = ?", topAgency).
		Where("ua.status = ?", 1).
		Where("r.slug IN ?", []string{"topAgency"}).
		Pluck("ru.user_id", &userIDs).Error

	if err != nil {
		return nil, err
	}

	return userIDs, nil
}
