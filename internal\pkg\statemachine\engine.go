package statemachine

import (
	"fmt"
	"marketing/internal/consts"
	"marketing/internal/pkg/errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

// EndpointApplicationStateMachine 终端申请状态机
type EndpointApplicationStateMachine struct {
	workflow   *WorkflowConfig
	validators map[string]Validator
	processors map[string]Processor
}

// ExecuteTransition 执行状态转换
func (sm *EndpointApplicationStateMachine) ExecuteTransition(ctx *gin.Context, req *StateTransitionRequest) error {
	// 检查工作流是否已加载
	if sm.workflow == nil {
		return errors.NewErr("工作流未初始化")
	}

	// 获取当前状态节点
	currentStateKey := strconv.Itoa(int(req.CurrentState))
	currentNode, exists := sm.workflow.States[currentStateKey]
	if !exists {
		return errors.NewErr(fmt.Sprintf("无效的当前状态: %d", req.CurrentState))
	}

	// 验证条件
	if err := sm.validateConditions(ctx, currentNode, req); err != nil {
		return err
	}

	// 验证全局业务规则
	if err := sm.validateGlobalRules(ctx, sm.workflow, req); err != nil {
		return err
	}

	// 若未提供动作但提供了目标状态，则尝试从目标状态反推出动作
	if req.Action == "" && req.TargetState != 0 {
		for action, ts := range currentNode.Transitions {
			if ts == req.TargetState {
				req.Action = action
				break
			}
		}
		if req.Action == "" {
			return errors.NewErr(fmt.Sprintf("无法从目标状态反推有效操作: %d", req.TargetState))
		}
	}

	// 获取下一状态
	nextState, exists := currentNode.Transitions[req.Action]
	if !exists {
		return errors.NewErr(fmt.Sprintf("当前状态 %s 不支持操作 %s", currentNode.Name, req.Action))
	}

	req.TargetState = nextState

	// 执行验证器
	if err := sm.runValidators(ctx, currentNode, req); err != nil {
		return err
	}

	// 执行状态转换
	return sm.executeStateChange(ctx, req)
}

// validateConditions 验证条件
func (sm *EndpointApplicationStateMachine) validateConditions(ctx *gin.Context, node *StateNode, req *StateTransitionRequest) error {
	for _, condition := range node.Conditions {
		if !sm.evaluateCondition(condition, req) {
			return errors.NewErr(fmt.Sprintf("条件验证失败: %s %s %v", condition.Field, condition.Operator, condition.Value))
		}
	}
	return nil
}

// validateGlobalRules 验证全局业务规则
func (sm *EndpointApplicationStateMachine) validateGlobalRules(ctx *gin.Context, workflow *WorkflowConfig, req *StateTransitionRequest) error {
	for _, rule := range workflow.GlobalRules {
		if sm.evaluateBusinessRule(rule, req) {
			if rule.Action == "reject" {
				return errors.NewErr(rule.Message)
			}
		}
	}
	return nil
}

// runValidators 运行验证器
func (sm *EndpointApplicationStateMachine) runValidators(ctx *gin.Context, node *StateNode, req *StateTransitionRequest) error {
	for _, validatorName := range node.Validators {
		if validator, exists := sm.validators[validatorName]; exists {
			if err := validator.Validate(ctx, req); err != nil {
				return err
			}
		}
	}
	return nil
}

// executeStateChange 执行状态变更
func (sm *EndpointApplicationStateMachine) executeStateChange(ctx *gin.Context, req *StateTransitionRequest) error {

	updateData := map[string]any{
		"state":      int(req.TargetState),
		"next_state": sm.getNextStateHint(req.TargetState),
	}

	// 获取目标状态的节点配置
	targetStateKey := strconv.Itoa(int(req.TargetState))
	targetNode, exists := sm.workflow.States[targetStateKey]
	if !exists {
		return errors.NewErr(fmt.Sprintf("无效的目标状态: %d", req.TargetState))
	}

	// 执行目标状态的处理器（而不是当前状态的处理器）
	//updateData 引用传递，处理器里面会影响到最终的更新数据
	for _, processorName := range targetNode.Processors {
		if processor, exists := sm.processors[processorName]; exists {
			fmt.Println("========执行处理器:", processorName)
			if err := processor.Process(ctx, req, updateData); err != nil {
				return err
			}
		}
	}

	// 更新申请记录状态（由状态机统一处理）
	if txRepo, exists := req.Data["txRepo"]; exists {
		if repo, ok := txRepo.(interface {
			UpdateEndpointApply(*gin.Context, int, map[string]any) error
		}); ok {
			if err := repo.UpdateEndpointApply(ctx, int(req.ApplicationID), updateData); err != nil {
				return err
			}
		}
	}

	return nil
}

// evaluateCondition 评估条件
func (sm *EndpointApplicationStateMachine) evaluateCondition(condition Condition, req *StateTransitionRequest) bool {
	value := sm.getFieldValue(condition.Field, req)

	switch condition.Operator {
	case "eq":
		return value == condition.Value
	case "gt":
		if v, ok := value.(float64); ok {
			if target, ok := condition.Value.(float64); ok {
				return v > target
			}
		}
	case "lt":
		if v, ok := value.(float64); ok {
			if target, ok := condition.Value.(float64); ok {
				return v < target
			}
		}
	case "in":
		if values, ok := condition.Value.([]interface{}); ok {
			for _, v := range values {
				if v == value {
					return true
				}
			}
		}
	case "not_in":
		if values, ok := condition.Value.([]interface{}); ok {
			for _, v := range values {
				if v == value {
					return false
				}
			}
			return true
		}
	}

	return false
}

// evaluateBusinessRule 评估业务规则
func (sm *EndpointApplicationStateMachine) evaluateBusinessRule(rule BusinessRule, req *StateTransitionRequest) bool {
	// 这里可以实现更复杂的规则引擎
	// 暂时返回false，表示规则不匹配
	return false
}

// getFieldValue 获取字段值
func (sm *EndpointApplicationStateMachine) getFieldValue(field string, req *StateTransitionRequest) interface{} {
	if value, exists := req.Data[field]; exists {
		return value
	}
	return nil
}

// getNextStateHint 获取下一状态提示
func (sm *EndpointApplicationStateMachine) getNextStateHint(currentState consts.EndpointApplicationState) int {
	if sm.workflow == nil {
		return 0
	}

	currentStateKey := strconv.Itoa(int(currentState))
	node, exists := sm.workflow.States[currentStateKey]
	if !exists {
		return 0
	}

	// 返回第一个可能的下一状态
	for _, nextState := range node.Transitions {
		return int(nextState)
	}

	return 0
}

// GetAvailableActions 获取可用操作
func (sm *EndpointApplicationStateMachine) GetAvailableActions(state consts.EndpointApplicationState) ([]Action, error) {
	if sm.workflow == nil {
		return nil, errors.NewErr("工作流未初始化")
	}

	stateKey := strconv.Itoa(int(state))
	node, exists := sm.workflow.States[stateKey]
	if !exists {
		return nil, errors.NewErr(fmt.Sprintf("无效的状态: %d", state))
	}

	return node.Actions, nil
}

// GetNextStates 获取下一状态列表
func (sm *EndpointApplicationStateMachine) GetNextStates(state consts.EndpointApplicationState) ([]consts.EndpointApplicationState, error) {
	if sm.workflow == nil {
		return nil, errors.NewErr("工作流未初始化")
	}

	stateKey := strconv.Itoa(int(state))
	node, exists := sm.workflow.States[stateKey]
	if !exists {
		return nil, errors.NewErr(fmt.Sprintf("无效的状态: %d", state))
	}

	var nextStates []consts.EndpointApplicationState
	for _, nextState := range node.Transitions {
		nextStates = append(nextStates, nextState)
	}

	return nextStates, nil
}

// RegisterValidator 注册验证器
func (sm *EndpointApplicationStateMachine) RegisterValidator(validator Validator) {
	sm.validators[validator.GetName()] = validator
}

// RegisterProcessor 注册处理器
func (sm *EndpointApplicationStateMachine) RegisterProcessor(processor Processor) {
	sm.processors[processor.GetName()] = processor
}

// LoadWorkflow 加载工作流
func (sm *EndpointApplicationStateMachine) LoadWorkflow(config *WorkflowConfig) error {
	sm.workflow = config
	return nil
}

// GetStateInfo 获取状态信息
func (sm *EndpointApplicationStateMachine) GetStateInfo(state consts.EndpointApplicationState) (*StateNode, error) {
	if sm.workflow == nil {
		return nil, errors.NewErr("工作流未初始化")
	}

	stateKey := strconv.Itoa(int(state))
	node, exists := sm.workflow.States[stateKey]
	if !exists {
		return nil, errors.NewErr(fmt.Sprintf("无效的状态: %d", state))
	}

	return node, nil
}

// GetWorkflowInfo 获取工作流信息
func (sm *EndpointApplicationStateMachine) GetWorkflowInfo() *WorkflowConfig {
	return sm.workflow
}
