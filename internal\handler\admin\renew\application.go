package renew

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"marketing/internal/api/renew"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/renew"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type ApplicationHandlerInterface interface {
	Audit(c *gin.Context)
	Lists(c *gin.Context)
	GetInfo(c *gin.Context)
	GetIssue(c *gin.Context)
	GetStatus(c *gin.Context)
	Completed(c *gin.Context)
	Export(c *gin.Context)
}

type application struct {
	svc service.ApplicationServiceInterface
}

func NewApplicationHandler(svc service.ApplicationServiceInterface) ApplicationHandlerInterface {
	return &application{
		svc: svc,
	}
}

func (h *application) Audit(c *gin.Context) {
	req := &renew.AuditApplicationReq{}
	if err := c.Should<PERSON>ind(req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	req.ID = id
	if req.Status == "passed" {
		req.Status = consts.RenewStatusHeadOfficeReview
	} else if req.Status == "rejected" {
		req.Status = consts.RenewStatusRejected
	}
	req.AuditType = consts.AdminPrefix

	err := h.svc.Audit(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *application) Lists(c *gin.Context) {
	req := &renew.ListApplicationReq{}
	if err := c.ShouldBind(req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	data, total, err := h.svc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":  data,
		"total": total,
	})
}

func (h *application) GetInfo(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	data, err := h.svc.GetInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (h *application) GetIssue(c *gin.Context) {
	data := consts.GetRenewIssues()
	handler.Success(c, data)
}

func (h *application) GetStatus(c *gin.Context) {
	data := consts.GetRenewStatusSlice()
	handler.Success(c, data)
}

func (h *application) Completed(c *gin.Context) {
	// 从表单中获取上传的文件
	file, err := c.FormFile("excelFile")
	if err != nil {
		handler.Error(c, appError.NewErr("获取上传的文件失败"))
		return
	}

	// 检查文件大小是否为 0
	if file.Size == 0 {
		handler.Error(c, appError.NewErr("上传的 Excel 文件不能为空"))
		return
	}

	// 生成随机文件名
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		handler.Error(c, err)
		return
	}
	randomFileName := hex.EncodeToString(bytes) + ".xlsx"
	filePath := "./logs/" + randomFileName

	// 保存文件到本地临时目录
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		handler.Error(c, err)
		return
	}

	// 读取 Excel 文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		// 删除临时文件
		if removeErr := os.Remove(filePath); removeErr != nil {
			handler.Error(c, removeErr)
			return
		}
		handler.Error(c, err)
		return
	}
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			handler.Error(c, closeErr)
		}
		if removeErr := os.Remove(filePath); removeErr != nil {
			handler.Error(c, removeErr)
		}
	}()

	// 这里可以添加读取 Excel 文件内容的逻辑
	sheetName := f.GetSheetName(0)
	// 获取第一列的内容
	cols, err := f.GetCols(sheetName)
	if err != nil {
		handler.Error(c, err)
		return
	}
	var firstColumnData []string
	if len(cols) > 0 {
		firstColumn := cols[1]
		for i, cellValue := range firstColumn {
			if i > 0 && cellValue != "" { // 检查单元格值是否为空
				firstColumnData = append(firstColumnData, cellValue)
			}
		}
	}
	// 检查第一列是否有数据
	if len(firstColumnData) == 0 {
		handler.Error(c, appError.NewErr("Excel 文件中第一列没有数据"))
		return
	}
	// 处理数据
	dataLen, err := h.svc.Completed(c, firstColumnData)

	handler.Success(c, fmt.Sprintf("成功处理%d条数据", dataLen))
}

func (h *application) Export(c *gin.Context) {
	var req renew.ListApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	err := h.svc.Export(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
}
