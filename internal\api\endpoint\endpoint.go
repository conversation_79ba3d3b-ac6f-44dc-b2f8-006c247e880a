package endpoint

import (
	"marketing/internal/api"
	"marketing/internal/model"
)

// AddEndpointReq 请求参数
type AddEndpointReq struct {
	ID           uint   `json:"-"`                       // 终端ID
	Name         string `json:"name" binding:"required"` // 终端名称
	Type         int8   `json:"type" binding:"required"` // 终端添加终端分类信息（1:专柜、2:运营商渠道、3:专卖店、4:城市综合体、5:商超）
	ChannelLevel uint8  `json:"channel_level"`           // 渠道等级，0-未知，1-省会级，2-地市级，3-县级，4-镇级
	Province     int    `json:"province" v:"required"`   // 省
	City         int    `json:"city" v:"required" `      // 市
	District     int    `json:"district"`                // 区
	Address      string `json:"address" v:"required"`    // 详细地址
	TopAgency    int    `json:"top_agency" v:"required"` // 一级代理
	SecondAgency int    `json:"second_agency"`           // 二级代理
	Phone        string `json:"phone" v:"required"`      // 电话
	Manager      string `json:"manager" v:"required"`    // 负责人
	Status       int8   `json:"status"`                  // 启用状态：0是禁用，1是启用
	OpenStatus   int8   `json:"-"`                       // 营业状态：0正常营业，1疑似歇业（非活跃），2歇业，3关店；open_status为0、1，status为1启用，为2、3，status为0禁用
	IsPreSale    int8   `json:"is_pre_sale"`             // 是否是售前(0不是,1是)
	IsAfterSale  int8   `json:"is_after_sale"`           // 是否是售后(0不是,1是)
	Images       string `json:"images"`                  // 终端形象图片
	Lng          string `json:"-"`                       // 经度（高德坐标）
	Lat          string `json:"-"`                       // 纬度（高德坐标）
	Blng         string `json:"blng" v:"required"`       // 经度（百度坐标）
	Blat         string `json:"blat" v:"required"`       // 纬度（百度坐标）
	License      string `json:"license"`                 // 营业执照
}

// GetEndpointReq 查询终端角色入参
type GetEndpointReq struct {
	api.PaginationParams
	Name         string `json:"name" form:"name"`
	Code         string `json:"code" form:"code"`
	Phone        string `json:"phone" form:"phone"`                 // 联系电话
	TopAgency    uint   `json:"top_agency" form:"top_agency"`       // 一级代理商名称
	SecondAgency uint   `json:"second_agency" form:"second_agency"` // 二级代理商名称
	Status       string `json:"status" form:"status"`               // 终端状态，1 为启用，0 为禁用
	IsPreSale    string `json:"is_pre_sale" form:"is_pre_sale"`     // 是否售前，1 为是，0 为否
	IsAfterSale  string `json:"is_after_sale" form:"is_after_sale"` // 是否售后，1 为是，0 为否
	OpenStatus   string `json:"open_status" form:"open_status"`     // 营业状态，1 为正常营业，2 为疑似歇业，3 为关店等
}

// UpdateStatusReq 更新状态请求
type UpdateStatusReq struct {
	ID     uint `json:"id" form:"id"`
	Status int8 `json:"status" form:"status" binding:"oneof=0 1"`
}

type ListEndpointRes struct {
	model.Endpoint
	AgencyName   string         `json:"agency_name"`
	ProvinceName string         `json:"province_name"`
	CityName     string         `json:"city_name"`
	DistrictName string         `json:"district_name"`
	CreatedAtStr string         `json:"created_at"`
	ActiveAtStr  string         `json:"active_at"`
	TypeName     string         `json:"type_name"`
	UpdatedTime  UpdateTimeData `json:"updated_time"`
}

type UpdateTimeData struct {
	Info  string `json:"info"`
	Image string `json:"image"`
	Data  string `json:"data"`
}

type EndpointInfoWithC struct {
	ID           int    `json:"column:id"`
	Name         string `json:"column:name"`
	Address      string `json:"column:address"`
	Phone        string `json:"column:phone"`
	Manager      string `json:"column:manager"`
	TopAgency    int    `json:"column:top_agency"`
	SecondAgency int    `json:"column:second_agency"`

	Channel string `json:"column:channel"` // agency表
}

type GetEndpointResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}
