package dao

import (
	"marketing/internal/model"
	"marketing/internal/pkg/types"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AppNotificationTabletUpdateDao interface {
	// BatchInsert 批量插入更新记录
	BatchInsert(c *gin.Context, updates []*model.AppNotificationTabletUpdate) error
	// GetDistinctDates 获取指定类型的不重复日期列表和总数
	GetDistinctDates(c *gin.Context, updateType string, pageNum, pageSize int) ([]string, int64, error)
	// GetByTypeAndDateRange 根据类型和日期范围获取记录
	GetByTypeAndDateRange(c *gin.Context, updateType, startDate, endDate string) ([]*model.AppNotificationTabletUpdate, error)
}

type appNotificationTabletUpdateDao struct {
	db *gorm.DB
}

func NewAppNotificationTabletUpdateDao(db *gorm.DB) AppNotificationTabletUpdateDao {
	return &appNotificationTabletUpdateDao{
		db: db,
	}
}

// BatchInsert 批量插入更新记录
func (d *appNotificationTabletUpdateDao) BatchInsert(c *gin.Context, updates []*model.AppNotificationTabletUpdate) error {
	if len(updates) == 0 {
		return nil
	}

	// 设置创建时间
	now := types.CustomTime(time.Now())
	for _, update := range updates {
		update.CreatedAt = now
	}

	return d.db.WithContext(c).Create(updates).Error
}

// GetDistinctDates 获取指定类型的不重复日期列表和总数
func (d *appNotificationTabletUpdateDao) GetDistinctDates(c *gin.Context, updateType string, pageNum, pageSize int) ([]string, int64, error) {
	var dates []string
	var total int64

	// 先获取总数
	err := d.db.WithContext(c).Model(&model.AppNotificationTabletUpdate{}).
		Where("type = ?", updateType).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页的日期列表
	query := d.db.WithContext(c).Model(&model.AppNotificationTabletUpdate{}).
		Select("DISTINCT DATE_FORMAT(updated_at, '%Y-%m-%d') as date").
		Where("type = ?", updateType).
		Order("date DESC")

	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err = query.Pluck("date", &dates).Error
	return dates, total, err
}

// GetByTypeAndDateRange 根据类型和日期范围获取记录
func (d *appNotificationTabletUpdateDao) GetByTypeAndDateRange(c *gin.Context, updateType, startDate, endDate string) ([]*model.AppNotificationTabletUpdate, error) {
	var updates []*model.AppNotificationTabletUpdate

	err := d.db.WithContext(c).
		Where("type = ?", updateType).
		Where("updated_at BETWEEN ? AND ?", startDate, endDate).
		Order("updated_at DESC, id ASC").
		Find(&updates).Error

	return updates, err
}
