package statemachine

import (
	"marketing/internal/consts"
	"marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

// BasicValidator 基础验证器
type BasicValidator struct{}

func (v *BasicValidator) GetName() string {
	return "basic_validator"
}

func (v *BasicValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 基础验证逻辑
	if req.ApplicationID == 0 {
		return errors.NewErr("申请ID不能为空")
	}
	
	if req.Action == "" {
		return errors.NewErr("操作类型不能为空")
	}
	
	return nil
}

// AuditValidator 审核验证器
type AuditValidator struct{}

func (v *AuditValidator) GetName() string {
	return "audit_validator"
}

func (v *AuditValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 审核验证逻辑
	if req.Action == "reject" {
		if remark, exists := req.Data["remark"]; !exists || remark == "" {
			return errors.NewErr("审核不通过时，备注不能为空")
		}
	}
	
	return nil
}

// MaterialValidator 物料验证器
type MaterialValidator struct{}

func (v *MaterialValidator) GetName() string {
	return "material_validator"
}

func (v *MaterialValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 物料验证逻辑
	if req.Action == "submit_material" {
		// 检查是否有物料支持
		if materialSupport, exists := req.Data["policy.material_support"]; exists {
			if support, ok := materialSupport.(int); ok && support != 1 {
				return errors.NewErr("当前政策不支持物料申请")
			}
		}
	}
	
	return nil
}

// WriteOffValidator 核销验证器
type WriteOffValidator struct{}

func (v *WriteOffValidator) GetName() string {
	return "writeoff_validator"
}

func (v *WriteOffValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 核销验证逻辑
	if req.Action == "writeoff_approve" {
		// 检查必要的核销文件
		requiredFiles := []string{"write_off_table", "lease_contract", "renovation_photos"}
		for _, file := range requiredFiles {
			if fileData, exists := req.Data[file]; !exists || fileData == "" {
				return errors.NewErr("核销文件不完整，请上传所有必要文件")
			}
		}
	}
	
	return nil
}

// AgencyValidator 代理商验证器
type AgencyValidator struct{}

func (v *AgencyValidator) GetName() string {
	return "agency_validator"
}

func (v *AgencyValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 代理商权限验证
	userID, exists := ctx.Get("user_id")
	if !exists {
		return errors.NewErr("用户未登录")
	}
	
	// 检查用户权限
	if userType, exists := req.Data["user_type"]; exists {
		if userType == "agency" && req.CurrentState == consts.ApplicationWaitingReview {
			// 代理商不能审核自己的申请
			if applicantID, exists := req.Data["applicant_id"]; exists {
				if applicantID == userID {
					return errors.NewErr("不能审核自己的申请")
				}
			}
		}
	}
	
	return nil
}

// FileValidator 文件验证器
type FileValidator struct{}

func (v *FileValidator) GetName() string {
	return "file_validator"
}

func (v *FileValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 文件验证逻辑
	if req.CurrentState == consts.ApplicationWaitingReview {
		// 检查基础文件
		if pics, exists := req.Data["pics"]; !exists || pics == "" {
			return errors.NewErr("请上传店铺照片")
		}
	}
	
	return nil
}

// AmountValidator 金额验证器
type AmountValidator struct{}

func (v *AmountValidator) GetName() string {
	return "amount_validator"
}

func (v *AmountValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 金额验证逻辑
	if req.Action == "amount_support" {
		if amountSupport, exists := req.Data["policy.amount_support"]; exists {
			if amount, ok := amountSupport.(float64); ok && amount <= 0 {
				return errors.NewErr("当前政策不支持金额补贴")
			}
		}
		
		// 检查申请金额是否超过政策限制
		if requestAmount, exists := req.Data["request_amount"]; exists {
			if policyAmount, exists := req.Data["policy.amount_support"]; exists {
				if reqAmt, ok := requestAmount.(float64); ok {
					if policyAmt, ok := policyAmount.(float64); ok {
						if reqAmt > policyAmt {
							return errors.NewErr("申请金额超过政策限制")
						}
					}
				}
			}
		}
	}
	
	return nil
}

// InstallmentValidator 分期验证器
type InstallmentValidator struct{}

func (v *InstallmentValidator) GetName() string {
	return "installment_validator"
}

func (v *InstallmentValidator) Validate(ctx *gin.Context, req *StateTransitionRequest) error {
	// 分期验证逻辑
	if req.Action == "record_confirmation" {
		if installments, exists := req.Data["policy.installments"]; exists {
			if periods, ok := installments.(int); ok && periods > 1 {
				// 检查分期相关信息
				if _, exists := req.Data["installment_plan"]; !exists {
					return errors.NewErr("分期政策需要提供分期计划")
				}
			}
		}
	}
	
	return nil
}

// NewDefaultValidators 创建默认验证器
func NewDefaultValidators() []Validator {
	return []Validator{
		&BasicValidator{},
		&AuditValidator{},
		&MaterialValidator{},
		&WriteOffValidator{},
		&AgencyValidator{},
		&FileValidator{},
		&AmountValidator{},
		&InstallmentValidator{},
	}
}
