package workflow

// WorkflowExecuteReq 通用工作流执行请求
type WorkflowExecuteReq struct {
	// 工作流类型 (如: "endpoint_application", "warranty", "reimbursement")
	WorkflowType string `json:"workflow_type" binding:"required"`

	// 实体ID
	EntityID uint `json:"entity_id" binding:"required"`

	// 执行的动作
	Action string `json:"action" binding:"required"`

	// 附加数据
	Data map[string]interface{} `json:"data"`

	// 备注
	Remark string `json:"remark"`

	// 扩展字段
	Extend map[string]interface{} `json:"extend"`
}

// WorkflowExecuteResp 工作流执行响应
type WorkflowExecuteResp struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	BeforeState int                    `json:"before_state"`
	AfterState  int                    `json:"after_state"`
	NextState   int                    `json:"next_state"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// GetWorkflowStateReq 获取工作流状态请求
type GetWorkflowStateReq struct {
	WorkflowType string `json:"workflow_type" form:"workflow_type" binding:"required"`
	EntityID     uint   `json:"entity_id" form:"entity_id" binding:"required"`
}

// GetWorkflowStateResp 获取工作流状态响应
type GetWorkflowStateResp struct {
	CurrentState     int          `json:"current_state"`
	NextState        int          `json:"next_state"`
	AvailableActions []ActionInfo `json:"available_actions"`
	WorkflowType     string       `json:"workflow_type"`
	EntityID         uint         `json:"entity_id"`
}

// ActionInfo 可用操作信息
type ActionInfo struct {
	Type        string                 `json:"type"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	TargetState int                    `json:"target_state"`
	Fields      []FieldInfo            `json:"fields,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// FieldInfo 字段信息
type FieldInfo struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	Label        string      `json:"label"`
	Required     bool        `json:"required"`
	Options      []Option    `json:"options,omitempty"`
	DefaultValue interface{} `json:"default_value,omitempty"`
}

// Option 选项
type Option struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}
