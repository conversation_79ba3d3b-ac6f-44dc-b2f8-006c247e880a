# 新的工作流架构示例

## 架构概述

新的架构完全消除了全局注册的复杂性，采用了更直观的Provider模式：

```
Service层 (endpointApplyService)
├── 实现 ValidatorProvider 接口
│   ├── GetValidator(name) -> 根据名称返回验证器实例
│   └── ListValidators() -> 返回支持的验证器列表
├── 实现 ProcessorProvider 接口
│   ├── GetProcessor(name) -> 根据名称返回处理器实例
│   └── ListProcessors() -> 返回支持的处理器列表
├── 内置验证器实现
│   ├── PermissionValidator - 权限验证
│   ├── DataValidator - 数据验证
│   └── PolicyValidator - 政策验证
└── 内置处理器实现
    ├── AuditProcessor - 审核处理
    ├── PostbackProcessor - 回传处理
    ├── MaterialProcessor - 物料处理
    └── WriteOffProcessor - 核销处理
```

## 工作流程

### 1. 创建状态机
```go
// 在Service中调用
stateMachine, err := statemachine.CreateStateMachineForPolicy(
    policyID,           // 政策ID
    e.workflowDao,      // 工作流配置DAO
    e,                  // ValidatorProvider (Service自己)
    e,                  // ProcessorProvider (Service自己)
)
```

### 2. 状态机工厂根据配置加载组件
```go
// 工厂内部逻辑
workflowConfig := loadWorkflowForPolicy(policyID)

// 根据配置加载验证器
for _, validatorName := range workflowConfig.Validators {
    validator, err := validatorProvider.GetValidator(validatorName)
    stateMachine.RegisterValidator(validator)
}

// 根据配置加载处理器
for _, processorName := range workflowConfig.Processors {
    processor, err := processorProvider.GetProcessor(processorName)
    stateMachine.RegisterProcessor(processor)
}
```

### 3. 配置示例
```json
{
  "name": "标准建店工作流",
  "slug": "standard",
  "validators": ["permission", "data"],
  "processors": ["audit", "postback"],
  "states": { ... }
}
```

## 优势

### 1. 清晰的职责分离
- **Service层**: 提供业务逻辑实现
- **工厂层**: 负责组装和配置
- **状态机层**: 负责流程控制

### 2. 配置驱动
- 不同政策可以配置不同的验证器和处理器组合
- 修改配置即可改变业务逻辑，无需修改代码

### 3. 易于扩展
```go
// 添加新的验证器
func (e endpointApplyService) GetValidator(name string) (statemachine.Validator, error) {
    switch name {
    case "permission":
        return &PermissionValidator{service: e}, nil
    case "data":
        return &DataValidator{service: e}, nil
    case "custom":  // 新增
        return &CustomValidator{service: e}, nil
    default:
        return nil, fmt.Errorf("未知的验证器: %s", name)
    }
}
```

### 4. 易于测试
```go
// 可以轻松Mock Provider
type MockValidatorProvider struct{}
func (m *MockValidatorProvider) GetValidator(name string) (Validator, error) {
    return &MockValidator{}, nil
}

// 测试时使用Mock
stateMachine := CreateStateMachineForPolicy(1, dao, mockValidatorProvider, mockProcessorProvider)
```

## 使用示例

### 1. 标准工作流配置
```json
{
  "validators": ["permission", "data", "policy"],
  "processors": ["audit", "postback", "material"]
}
```
- 严格的权限验证
- 完整的数据验证
- 政策合规性检查
- 支持审核、回传、物料全流程

### 2. 快速工作流配置
```json
{
  "validators": ["permission"],
  "processors": ["audit", "postback"]
}
```
- 只验证权限
- 简化的审核和回传流程

### 3. 测试工作流配置
```json
{
  "validators": [],
  "processors": ["audit"]
}
```
- 跳过所有验证
- 只保留基本的审核处理

## 扩展示例

### 添加新的验证器
```go
// 在Service中添加新的验证器类型
type CustomValidator struct {
    service endpointApplyService
}

func (v *CustomValidator) Validate(ctx *gin.Context, req *statemachine.StateTransitionRequest) error {
    // 自定义验证逻辑
    return nil
}

func (v *CustomValidator) GetName() string {
    return "custom"
}

// 在GetValidator方法中添加case
case "custom":
    return &CustomValidator{service: e}, nil
```

### 添加新的处理器
```go
// 在Service中添加新的处理器类型
type NotificationProcessor struct {
    service endpointApplyService
}

func (p *NotificationProcessor) Process(ctx *gin.Context, req *statemachine.StateTransitionRequest, updateData map[string]any) error {
    // 发送通知逻辑
    return nil
}

func (p *NotificationProcessor) GetName() string {
    return "notification"
}

// 在GetProcessor方法中添加case
case "notification":
    return &NotificationProcessor{service: e}, nil
```

## 总结

新架构的核心思想：
1. **Service层拥有所有业务逻辑实现**
2. **配置决定使用哪些组件**
3. **工厂负责按配置组装**
4. **无需全局注册，按需实例化**

这样的设计更加直观、灵活，易于理解和维护。
