package endpoint_application

import (
	"fmt"
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/consts"
	userDao "marketing/internal/dao/admin_user"
	applicationDao "marketing/internal/dao/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"marketing/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InstallmentService interface {
	// RecordAccount 入账操作
	RecordAccount(c *gin.Context, req *api.AccountRecordReq) error

	// GetInstallmentList 获取分期列表
	GetInstallmentList(c *gin.Context, req *api.InstallmentListReq) ([]*api.InstallmentListResp, int64, error)

	// GetInstallmentByID 根据ID获取分期记录
	GetInstallmentByID(c *gin.Context, id uint) (*api.InstallmentListResp, error)

	// GetInstallmentsByApplication 根据申请ID获取分期列表（不分页）
	GetInstallmentsByApplication(c *gin.Context, req *api.InstallmentsByApplicationReq) ([]*api.InstallmentListResp, error)
}

type installmentService struct {
	installmentDao   applicationDao.InstallmentDao
	endpointApplyDao applicationDao.EndpointApplyDao
	userDao          userDao.UserDao
	pushSvc          service.PushNotificationSvc
}

// NewInstallmentService 创建分期服务实例
func NewInstallmentService(
	installmentDao applicationDao.InstallmentDao,
	endpointApplyDao applicationDao.EndpointApplyDao,
	userDao userDao.UserDao,
	pushSvc service.PushNotificationSvc,
) InstallmentService {
	return &installmentService{
		installmentDao:   installmentDao,
		endpointApplyDao: endpointApplyDao,
		userDao:          userDao,
		pushSvc:          pushSvc,
	}
}

// RecordAccount 入账操作
func (s *installmentService) RecordAccount(c *gin.Context, req *api.AccountRecordReq) error {
	// 获取分期记录
	installment, err := s.installmentDao.GetInstallmentByID(c, req.ID)
	if err != nil {
		return err
	}
	if installment == nil {
		return errors.NewErr("分期记录不存在")
	}

	// 获取申请记录
	application, err := s.endpointApplyDao.GetEndpointApplyByID(c, int(installment.ApplicationID))
	if err != nil {
		return err
	}
	if application == nil {
		return errors.NewErr("申请记录不存在")
	}
	// 检查当前状态是否允许入账
	if application.State == int(consts.ApplicationCancelled) {
		return errors.NewErr("建店已中止，无法入账")
	}

	// 获取当前用户ID作为入账人
	recordedBy := c.GetUint("uid")

	// 执行入账操作
	if err := s.installmentDao.RecordAccount(c, req.ID, req.SupportStatus, recordedBy, req.RecordRemark); err != nil {
		return err
	}

	// 如果是入账成功（status=1），发送通知
	if req.SupportStatus == 1 {
		go s.sendRecordNotification(c, installment, application)
	}

	return nil
}

// sendRecordNotification 发送入账通知
func (s *installmentService) sendRecordNotification(c *gin.Context, installment *model.EndpointApplicationInstallment, application *model.EndpointApplication) {
	// 获取同申请的所有分期记录，按日期排序
	installments, err := s.installmentDao.GetInstallmentsByApplicationID(c, installment.ApplicationID)
	if err != nil {
		log.Error("获取分期记录失败", zap.Error(err), zap.Uint("application_id", installment.ApplicationID))
		return
	}

	// 找到当前分期是第几期
	var index int
	for i, inst := range installments {
		if inst.ID == installment.ID {
			index = i + 1
			break
		}
	}

	// 构建通知内容
	year := installment.Date.Year()
	month := int(installment.Date.Month())
	title := "分期支持费用入账通知"
	content := fmt.Sprintf("您的店铺【%s】，%d年%d月第%d期支持费用已成功入账，请查收！",
		application.Name, year, month, index)

	// 获取推送目标用户
	audience := s.getNotificationAudience(c, application)
	if audience == nil {
		log.Warn("无法确定通知目标用户", zap.Int("application_id", application.ID))
		return
	}

	// 调用推送通知服务
	err = s.pushSvc.PushNotification(c, &service.PushNotificationParams{
		Title:    title,
		Content:  content,
		Platform: []string{"wecom"},
		Audience: audience,
		Slug:     "endpoint_application_record", // 需要在数据库中配置对应的通知类型
		Action:   "none",                        // 或者根据需要设置跳转动作
	})

	if err != nil {
		log.Error("发送入账通知失败", zap.Error(err),
			zap.Uint("installment_id", installment.ID),
			zap.Int("application_id", application.ID))
	} else {
		log.Info("入账通知发送成功",
			zap.Uint("installment_id", installment.ID),
			zap.Int("application_id", application.ID),
			zap.String("content", content))
	}
}

// getNotificationAudience 获取通知推送目标
func (s *installmentService) getNotificationAudience(c *gin.Context, application *model.EndpointApplication) map[string]interface{} {
	// 只推送给与店铺相关的总代用户
	if application.TopAgency > 0 {
		// 通过 userDao 获取与该店铺总代相关的用户ID列表
		userIDs, err := s.userDao.GetTopAgencyUsersByAgency(c, application.TopAgency)
		if err != nil {
			log.Error("获取总代用户失败", zap.Error(err), zap.Uint("top_agency", application.TopAgency))
			return nil
		}

		if len(userIDs) > 0 {

			return map[string]interface{}{
				"users": userIDs,
			}
		}
	}

	// 如果没有找到总代用户，返回nil，表示无法确定推送目标
	return nil
}

// GetInstallmentList 获取分期列表
func (s *installmentService) GetInstallmentList(c *gin.Context, req *api.InstallmentListReq) ([]*api.InstallmentListResp, int64, error) {
	// 设置默认分页参数
	req.SetDefaults()

	// 获取分期列表
	installments, total, err := s.installmentDao.GetInstallmentList(c, req)
	if err != nil {
		return nil, 0, err
	}

	if len(installments) == 0 {
		return []*api.InstallmentListResp{}, total, nil
	}

	// 收集申请ID，用于获取申请名称
	applicationIDs := make([]int, 0)
	for _, item := range installments {
		applicationIDs = append(applicationIDs, int(item.ApplicationID))
	}

	// 获取申请信息
	applications := make(map[uint]string)
	if len(applicationIDs) > 0 {
		// 这里需要调用endpointApplyDao获取申请名称
		// 为了简化，先使用默认值，实际项目中需要实现相应的DAO方法
	}

	// 转换为响应格式
	var result []*api.InstallmentListResp
	for _, item := range installments {
		resp := &api.InstallmentListResp{
			ID:                 item.ID,
			ApplicationID:      item.ApplicationID,
			ApplicationName:    applications[item.ApplicationID],
			Year:               item.Year,
			Month:              item.Month,
			Date:               types.DateOnly(item.Date),
			Fee:                item.Fee,
			StaffFee:           item.StaffFee,
			SupportStatus:      item.SupportStatus,
			StaffSupportStatus: item.StaffSupportStatus,
			StaffRecordRemark:  item.StaffRecordRemark,
			RecordedBy:         item.RecordedBy,
			RecordedAt:         types.CustomTime(item.RecordedAt),
			RecordRemark:       item.RecordRemark,
			CreatedAt:          types.CustomTime(item.CreatedAt),
			UpdatedAt:          types.CustomTime(item.UpdatedAt),
		}

		// 设置状态名称
		resp.SupportStatusName = getSupportStatusName(item.SupportStatus)

		result = append(result, resp)
	}

	return result, total, nil
}

// GetInstallmentByID 根据ID获取分期记录
func (s *installmentService) GetInstallmentByID(c *gin.Context, id uint) (*api.InstallmentListResp, error) {
	installment, err := s.installmentDao.GetInstallmentByID(c, id)
	if err != nil {
		return nil, err
	}
	if installment == nil {
		return nil, errors.NewErr("分期记录不存在")
	}

	resp := &api.InstallmentListResp{
		ID:                 installment.ID,
		ApplicationID:      installment.ApplicationID,
		Year:               installment.Year,
		Month:              installment.Month,
		Date:               types.DateOnly(installment.Date),
		Fee:                installment.Fee,
		StaffFee:           installment.StaffFee,
		SupportStatus:      installment.SupportStatus,
		StaffSupportStatus: installment.StaffSupportStatus,
		StaffRecordRemark:  installment.StaffRecordRemark,
		RecordedBy:         installment.RecordedBy,
		RecordedAt:         types.CustomTime(installment.RecordedAt),
		RecordRemark:       installment.RecordRemark,
		CreatedAt:          types.CustomTime(installment.CreatedAt),
		UpdatedAt:          types.CustomTime(installment.UpdatedAt),
	}

	// 设置状态名称
	resp.SupportStatusName = getSupportStatusName(installment.SupportStatus)

	return resp, nil
}

// getSupportStatusName 获取入账状态名称
func getSupportStatusName(status uint8) string {
	switch status {
	case 0:
		return "未入账"
	case 1:
		return "已入账"
	case 2:
		return "中止"
	default:
		return "未知"
	}
}

// GetInstallmentsByApplication 根据申请ID获取分期列表（不分页）
func (s *installmentService) GetInstallmentsByApplication(c *gin.Context, req *api.InstallmentsByApplicationReq) ([]*api.InstallmentListResp, error) {
	// 获取分期数据
	installments, err := s.installmentDao.GetInstallmentsByApplicationID(c, req.ApplicationID)
	if err != nil {
		return nil, err
	}

	if len(installments) == 0 {
		return []*api.InstallmentListResp{}, nil
	}

	// 获取申请信息
	application, err := s.endpointApplyDao.GetEndpointApplyByID(c, int(req.ApplicationID))
	if err != nil {
		log.Error("获取申请信息失败", zap.Error(err), zap.Uint("application_id", req.ApplicationID))
		// 即使获取申请信息失败，也返回分期数据，只是申请名称为空
	}

	// 转换为响应格式
	var result []*api.InstallmentListResp
	for _, installment := range installments {
		resp := &api.InstallmentListResp{
			ID:                 installment.ID,
			ApplicationID:      installment.ApplicationID,
			Year:               installment.Year,
			Month:              installment.Month,
			Date:               types.DateOnly(installment.Date),
			Fee:                installment.Fee,
			StaffFee:           installment.StaffFee,
			SupportStatus:      installment.SupportStatus,
			StaffSupportStatus: installment.StaffSupportStatus,
			StaffRecordRemark:  installment.StaffRecordRemark,
			RecordedBy:         installment.RecordedBy,
			RecordedAt:         types.CustomTime(installment.RecordedAt),
			RecordRemark:       installment.RecordRemark,
			CreatedAt:          types.CustomTime(installment.CreatedAt),
			UpdatedAt:          types.CustomTime(installment.UpdatedAt),
		}

		// 设置申请名称
		if application != nil {
			resp.ApplicationName = application.Name
		}

		// 设置入账状态名称
		resp.SupportStatusName = getSupportStatusName(installment.SupportStatus)

		// 获取入账人姓名
		if installment.RecordedBy > 0 {
			if userName, err := s.userDao.GetSalesmanByUserID(c, installment.RecordedBy); err == nil {
				resp.RecordedByName = userName
			}
		}

		result = append(result, resp)
	}

	return result, nil
}
