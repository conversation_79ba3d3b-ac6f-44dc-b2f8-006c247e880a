package statemachine

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
)

// WorkflowLoader 工作流加载器接口
type WorkflowLoader interface {
	LoadWorkflowFromFile(filename string) (*WorkflowConfig, error)
	SaveWorkflowToFile(config *WorkflowConfig, filename string) error
	ValidateWorkflow(config *WorkflowConfig) error
}

// FileWorkflowLoader 文件工作流加载器
type FileWorkflowLoader struct {
	configPath string
}

// DatabaseWorkflowLoader 数据库工作流加载器
type DatabaseWorkflowLoader struct {
	workflowDao WorkflowConfigDao
}

// WorkflowConfigDao 工作流配置数据访问接口
type WorkflowConfigDao interface {
	GetStandardWorkflowConfig(ctx interface{}) (*WorkflowConfigModel, error)
	GetPolicyByID(policyID int) (PolicyModel, error)
	GetWorkflowTemplateByID(templateID int) (WorkflowTemplateModel, error)
}

// WorkflowConfigModel 工作流配置模型接口
type WorkflowConfigModel interface {
	GetWorkflowConfig() string
	GetVersion() string
	IsActive() bool
}

// PolicyModel 政策模型接口
type PolicyModel interface {
	GetID() int
	GetWorkflowTemplate() int
}

// WorkflowTemplateModel 工作流模板模型接口
type WorkflowTemplateModel interface {
	GetID() int
	GetName() string
	GetSlug() string
	GetConfig() string
}

// NewFileWorkflowLoader 创建文件工作流加载器
func NewFileWorkflowLoader(configPath string) WorkflowLoader {
	return &FileWorkflowLoader{
		configPath: configPath,
	}
}

// NewDatabaseWorkflowLoader 创建数据库工作流加载器
func NewDatabaseWorkflowLoader(dao WorkflowConfigDao) WorkflowLoader {
	return &DatabaseWorkflowLoader{
		workflowDao: dao,
	}
}

// FileWorkflowLoader 方法实现

// LoadWorkflowFromFile 从文件加载工作流配置
func (l *FileWorkflowLoader) LoadWorkflowFromFile(filename string) (*WorkflowConfig, error) {
	filePath := filepath.Join(l.configPath, filename)

	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取工作流配置文件失败: %w", err)
	}

	var config WorkflowConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析工作流配置失败: %w", err)
	}

	return &config, nil
}

// SaveWorkflowToFile 保存工作流配置到文件
func (l *FileWorkflowLoader) SaveWorkflowToFile(config *WorkflowConfig, filename string) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化工作流配置失败: %w", err)
	}

	filePath := filepath.Join(l.configPath, filename)
	if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("保存工作流配置文件失败: %w", err)
	}

	return nil
}

// ValidateWorkflow 验证工作流配置的完整性
func (l *FileWorkflowLoader) ValidateWorkflow(config *WorkflowConfig) error {

	if config.Name == "" {
		return fmt.Errorf("工作流名称不能为空")
	}

	if len(config.States) == 0 {
		return fmt.Errorf("工作流必须包含至少一个状态")
	}

	// 检查起始状态是否存在
	startStateKey := fmt.Sprintf("%d", config.StartState)
	if _, exists := config.States[startStateKey]; !exists {
		return fmt.Errorf("起始状态 %d 不存在", config.StartState)
	}

	// 检查状态转换的完整性
	for stateKey, state := range config.States {
		for action, nextState := range state.Transitions {
			nextStateKey := fmt.Sprintf("%d", nextState)
			if _, exists := config.States[nextStateKey]; !exists {
				return fmt.Errorf("状态 %s 的动作 %s 指向的下一状态 %d 不存在", stateKey, action, nextState)
			}
		}
	}

	return nil
}

// DatabaseWorkflowLoader 方法实现

// LoadWorkflowFromFile 数据库加载器不支持从文件加载
func (l *DatabaseWorkflowLoader) LoadWorkflowFromFile(filename string) (*WorkflowConfig, error) {
	return nil, fmt.Errorf("数据库加载器不支持从文件加载工作流")
}

// LoadStandardWorkflow 从数据库加载标准工作流配置
func (l *DatabaseWorkflowLoader) LoadStandardWorkflow() (*WorkflowConfig, error) {
	// 从数据库获取标准工作流配置
	configModel, err := l.workflowDao.GetStandardWorkflowConfig(nil)
	if err != nil {
		return nil, fmt.Errorf("从数据库获取工作流配置失败: %w", err)
	}

	// 解析JSON配置
	var config WorkflowConfig
	if err := json.Unmarshal([]byte((*configModel).GetWorkflowConfig()), &config); err != nil {
		return nil, fmt.Errorf("解析工作流配置失败: %w", err)
	}

	return &config, nil
}

// SaveWorkflowToFile 数据库加载器不支持保存到文件
func (l *DatabaseWorkflowLoader) SaveWorkflowToFile(config *WorkflowConfig, filename string) error {
	return fmt.Errorf("数据库加载器不支持保存工作流到文件")
}

// ValidateWorkflow 验证工作流配置的完整性
func (l *DatabaseWorkflowLoader) ValidateWorkflow(config *WorkflowConfig) error {

	if config.Name == "" {
		return fmt.Errorf("工作流名称不能为空")
	}

	if len(config.States) == 0 {
		return fmt.Errorf("工作流必须包含至少一个状态")
	}

	// 检查起始状态是否存在
	startStateKey := fmt.Sprintf("%d", config.StartState)
	if _, exists := config.States[startStateKey]; !exists {
		return fmt.Errorf("起始状态 %d 不存在", config.StartState)
	}

	// 检查状态转换的完整性
	for stateKey, state := range config.States {
		for action, nextState := range state.Transitions {
			nextStateKey := fmt.Sprintf("%d", nextState)
			if _, exists := config.States[nextStateKey]; !exists {
				return fmt.Errorf("状态 %s 的动作 %s 指向的下一状态 %d 不存在", stateKey, action, nextState)
			}
		}
	}

	return nil
}
